"""
Views for PDF generation and template management.

This module provides views for:
- Template preview generation
- Code generation requests
- Template management
- Real-time preview interface
"""

import json
import logging
from typing import Dict, Any
from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse, Http404
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from .models import PDFTemplate, GeneratedCode, TemplatePreview, TemplateVersion
from .services import PDFGenerationService
from .tasks import generate_code_async, generate_preview_async
from .engines import get_supported_languages

logger = logging.getLogger(__name__)


class TemplateListView(LoginRequiredMixin, ListView):
    """List view for PDF templates."""
    model = PDFTemplate
    template_name = 'pdf_generation/template_list.html'
    context_object_name = 'templates'
    paginate_by = 20

    def get_queryset(self):
        queryset = PDFTemplate.objects.filter(created_by=self.request.user)

        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        # Status filter
        status_filter = self.request.GET.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset.order_by('-updated_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = PDFTemplate.Status.choices
        context['search'] = self.request.GET.get('search', '')
        context['status_filter'] = self.request.GET.get('status', '')
        return context


class TemplateCreateView(LoginRequiredMixin, CreateView):
    """Create view for PDF templates."""
    model = PDFTemplate
    template_name = 'pdf_generation/template_create.html'
    fields = [
        'name', 'description', 'organization', 'page_size', 'orientation',
        'custom_width', 'custom_height', 'margin_top', 'margin_bottom',
        'margin_left', 'margin_right', 'parent_template'
    ]

    def get_form(self, form_class=None):
        """Add CSS classes to form fields."""
        form = super().get_form(form_class)

        # Common CSS classes for form fields
        text_input_classes = "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        select_classes = "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        textarea_classes = "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical"
        number_input_classes = "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"

        # Apply CSS classes to form fields
        form.fields['name'].widget.attrs.update({'class': text_input_classes, 'placeholder': 'Enter template name'})
        form.fields['description'].widget.attrs.update({'class': textarea_classes, 'rows': 3, 'placeholder': 'Describe your template'})
        form.fields['organization'].widget.attrs.update({'class': text_input_classes, 'placeholder': 'Organization name (optional)'})
        form.fields['page_size'].widget.attrs.update({'class': select_classes})
        form.fields['orientation'].widget.attrs.update({'class': select_classes})
        form.fields['custom_width'].widget.attrs.update({'class': number_input_classes, 'placeholder': '210'})
        form.fields['custom_height'].widget.attrs.update({'class': number_input_classes, 'placeholder': '297'})
        form.fields['margin_top'].widget.attrs.update({'class': number_input_classes})
        form.fields['margin_bottom'].widget.attrs.update({'class': number_input_classes})
        form.fields['margin_left'].widget.attrs.update({'class': number_input_classes})
        form.fields['margin_right'].widget.attrs.update({'class': number_input_classes})
        form.fields['parent_template'].widget.attrs.update({'class': select_classes})

        return form

    def form_valid(self, form):
        """Set the created_by field to the current user."""
        form.instance.created_by = self.request.user
        return super().form_valid(form)

    def get_success_url(self):
        """Redirect to the template detail page after creation."""
        from django.urls import reverse
        return reverse('pdf_generation:template_detail', kwargs={'slug': self.object.slug})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_size_choices'] = PDFTemplate.PageSize.choices
        context['orientation_choices'] = PDFTemplate.Orientation.choices
        context['status_choices'] = PDFTemplate.Status.choices
        # Get available parent templates for the current user
        context['parent_templates'] = PDFTemplate.objects.filter(
            created_by=self.request.user,
            status__in=[PDFTemplate.Status.ACTIVE, PDFTemplate.Status.DRAFT]
        ).order_by('name')
        return context


class TemplateDetailView(LoginRequiredMixin, DetailView):
    """Detail view for PDF template."""
    model = PDFTemplate
    template_name = 'pdf_generation/template_detail.html'
    context_object_name = 'template'

    def get_queryset(self):
        return PDFTemplate.objects.filter(created_by=self.request.user)

    def dispatch(self, request, *args, **kwargs):
        """Override dispatch to handle UUID-to-slug redirects."""
        from django.shortcuts import redirect
        from django.urls import reverse
        import uuid

        # Check if we have a UUID parameter (from UUID-based URL)
        pk = kwargs.get('pk')

        if pk:
            # UUID-based access - redirect to slug-based URL for SEO
            try:
                uuid_obj = uuid.UUID(str(pk))
                template = self.get_queryset().get(id=uuid_obj)
                # Redirect to slug-based URL
                return redirect('pdf_generation:template_detail', slug=template.slug)
            except (ValueError, TypeError, PDFTemplate.DoesNotExist):
                pass

        return super().dispatch(request, *args, **kwargs)

    def get_object(self, queryset=None):
        """Override to handle slug-based lookups."""
        slug = self.kwargs.get('slug')
        if slug:
            try:
                return self.get_queryset().get(slug=slug)
            except PDFTemplate.DoesNotExist:
                pass

        # Fallback to default behavior
        return super().get_object(queryset)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        template = self.get_object()

        # Get recent generated code
        context['generated_codes'] = GeneratedCode.objects.filter(
            template=template
        ).order_by('-created_at')[:5]

        # Get recent previews
        context['previews'] = TemplatePreview.objects.filter(
            template=template
        ).order_by('-created_at')[:5]

        # Get template versions
        context['versions'] = TemplateVersion.objects.filter(
            template=template
        ).order_by('-created_at')[:10]

        # Supported languages
        context['supported_languages'] = get_supported_languages()

        return context


@login_required
@require_http_methods(["GET"])
def template_preview_view(request, template_id=None, template_slug=None):
    """Template preview interface."""
    from django.shortcuts import redirect
    from django.urls import reverse
    import uuid

    if template_id:
        # UUID-based access - redirect to slug-based URL for SEO
        try:
            uuid_obj = uuid.UUID(str(template_id))
            template = get_object_or_404(
                PDFTemplate,
                id=uuid_obj,
                created_by=request.user
            )
            # Redirect to slug-based URL
            return redirect('pdf_generation:template_preview', template_slug=template.slug)
        except (ValueError, TypeError):
            # Not a valid UUID, treat as slug
            template = get_object_or_404(
                PDFTemplate,
                slug=template_id,
                created_by=request.user
            )
    elif template_slug:
        # Slug-based access - normal lookup
        template = get_object_or_404(
            PDFTemplate,
            slug=template_slug,
            created_by=request.user
        )
    else:
        # No identifier provided
        from django.http import Http404
        raise Http404("Template not found")

    context = {
        'template': template,
        'elements': template.elements.all().order_by('order'),
        'sample_data': json.dumps({
            'title': f'Preview of {template.name}',
            'date': timezone.now().strftime('%Y-%m-%d'),
            'content': 'This is a preview of your PDF template.',
            'items': [
                {'name': 'Sample Item 1', 'value': 100},
                {'name': 'Sample Item 2', 'value': 200},
            ],
            'total': 300,
            'customer': {
                'name': 'John Doe',
                'email': '<EMAIL>'
            }
        }, indent=2)
    }

    return render(request, 'pdf_generation/template_preview.html', context)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def generate_preview_api(request, template_id):
    """API endpoint for generating template preview."""
    try:
        template = PDFTemplate.objects.get(
            id=template_id,
            created_by=request.user
        )
    except PDFTemplate.DoesNotExist:
        return Response(
            {'error': 'Template not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    # Get sample data from request
    sample_data = request.data.get('sample_data', {})

    # Start async preview generation
    task = generate_preview_async.delay(
        str(template.id),
        sample_data,
        request.user.id
    )

    return Response({
        'success': True,
        'task_id': task.id,
        'message': 'Preview generation started'
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def preview_status_api(request, preview_id):
    """API endpoint for checking preview generation status."""
    try:
        preview = TemplatePreview.objects.get(
            id=preview_id,
            generated_by=request.user
        )
    except TemplatePreview.DoesNotExist:
        return Response(
            {'error': 'Preview not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    # Create a simple serializer response
    data = {
        'id': str(preview.id),
        'status': preview.status,
        'created_at': preview.created_at.isoformat(),
        'generation_time': preview.generation_time,
        'error_message': preview.error_message,
        'preview_pdf_url': preview.preview_pdf.url if preview.preview_pdf else None,
        'preview_image_url': preview.preview_image.url if preview.preview_image else None,
    }

    return Response(data)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def generate_code_api(request, template_id):
    """API endpoint for generating code."""
    try:
        template = PDFTemplate.objects.get(
            id=template_id,
            created_by=request.user
        )
    except PDFTemplate.DoesNotExist:
        return Response(
            {'error': 'Template not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    # Validate language
    language = request.data.get('language')
    if not language or language not in get_supported_languages():
        return Response(
            {'error': 'Invalid or missing language'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Get generation options
    options = request.data.get('options', {})

    # Start async code generation
    task = generate_code_async.delay(
        str(template.id),
        language,
        options,
        request.user.id
    )

    return Response({
        'success': True,
        'task_id': task.id,
        'message': 'Code generation started'
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def code_generation_status_api(request, generated_code_id):
    """API endpoint for checking code generation status."""
    try:
        generated_code = GeneratedCode.objects.get(
            id=generated_code_id,
            generated_by=request.user
        )
    except GeneratedCode.DoesNotExist:
        return Response(
            {'error': 'Generated code not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    # Create a simple serializer response
    data = {
        'id': str(generated_code.id),
        'language': generated_code.language,
        'status': generated_code.status,
        'created_at': generated_code.created_at.isoformat(),
        'generation_time': generated_code.generation_time,
        'error_message': generated_code.error_message,
        'download_count': generated_code.download_count,
        'code_package_url': generated_code.code_package.url if generated_code.code_package else None,
    }

    return Response(data)


@login_required
@require_http_methods(["GET"])
def download_generated_code(request, generated_code_id):
    """Download generated code package."""
    try:
        generated_code = GeneratedCode.objects.get(
            id=generated_code_id,
            generated_by=request.user,
            status=GeneratedCode.Status.COMPLETED
        )
    except GeneratedCode.DoesNotExist:
        raise Http404("Generated code not found")

    if not generated_code.code_package:
        raise Http404("Code package not available")

    # Increment download count
    generated_code.increment_download_count()

    # Serve file
    response = HttpResponse(
        generated_code.code_package.read(),
        content_type='application/zip'
    )

    filename = f"{generated_code.template.slug}_{generated_code.language}.zip"
    response['Content-Disposition'] = f'attachment; filename="{filename}"'

    return response


@login_required
@require_http_methods(["GET"])
def download_preview_pdf(request, preview_id):
    """Download preview PDF."""
    try:
        preview = TemplatePreview.objects.get(
            id=preview_id,
            generated_by=request.user,
            status=TemplatePreview.PreviewStatus.COMPLETED
        )
    except TemplatePreview.DoesNotExist:
        raise Http404("Preview not found")

    if not preview.preview_pdf:
        raise Http404("Preview PDF not available")

    # Serve file
    response = HttpResponse(
        preview.preview_pdf.read(),
        content_type='application/pdf'
    )

    filename = f"preview_{preview.template.slug}.pdf"
    response['Content-Disposition'] = f'attachment; filename="{filename}"'

    return response


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def template_validation_api(request, template_id):
    """API endpoint for validating template."""
    try:
        template = PDFTemplate.objects.get(
            id=template_id,
            created_by=request.user
        )
    except PDFTemplate.DoesNotExist:
        return Response(
            {'error': 'Template not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    # Validate template
    service = PDFGenerationService()
    is_valid, errors = service.validate_template(template)

    return Response({
        'valid': is_valid,
        'errors': errors,
        'element_count': template.elements.count(),
        'last_updated': template.updated_at.isoformat()
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def supported_languages_api(request):
    """API endpoint for getting supported languages."""
    languages = get_supported_languages()

    # Add language metadata
    language_info = {
        'python': {
            'name': 'Python',
            'description': 'Generate Python code using FPDF2 and ReportLab',
            'file_extension': '.py',
            'package_manager': 'pip'
        },
        'javascript': {
            'name': 'JavaScript',
            'description': 'Generate JavaScript code using jsPDF',
            'file_extension': '.js',
            'package_manager': 'npm'
        },
        'php': {
            'name': 'PHP',
            'description': 'Generate PHP code using TCPDF',
            'file_extension': '.php',
            'package_manager': 'composer'
        }
    }

    return Response({
        'languages': languages,
        'language_info': {lang: language_info.get(lang, {}) for lang in languages}
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_stats_api(request):
    """API endpoint for dashboard statistics."""
    user = request.user

    # Template statistics
    template_stats = {
        'total': PDFTemplate.objects.filter(created_by=user).count(),
        'active': PDFTemplate.objects.filter(
            created_by=user,
            status=PDFTemplate.Status.ACTIVE
        ).count(),
        'draft': PDFTemplate.objects.filter(
            created_by=user,
            status=PDFTemplate.Status.DRAFT
        ).count()
    }

    # Generation statistics
    generation_stats = {
        'total': GeneratedCode.objects.filter(generated_by=user).count(),
        'completed': GeneratedCode.objects.filter(
            generated_by=user,
            status=GeneratedCode.Status.COMPLETED
        ).count(),
        'failed': GeneratedCode.objects.filter(
            generated_by=user,
            status=GeneratedCode.Status.FAILED
        ).count()
    }

    # Preview statistics
    preview_stats = {
        'total': TemplatePreview.objects.filter(generated_by=user).count(),
        'completed': TemplatePreview.objects.filter(
            generated_by=user,
            status=TemplatePreview.PreviewStatus.COMPLETED
        ).count()
    }

    # Recent activity - simplified without serializers for now
    recent_templates = PDFTemplate.objects.filter(
        created_by=user
    ).order_by('-updated_at')[:5]

    recent_generations = GeneratedCode.objects.filter(
        generated_by=user
    ).order_by('-created_at')[:5]

    return Response({
        'template_stats': template_stats,
        'generation_stats': generation_stats,
        'preview_stats': preview_stats,
        'recent_templates': [
            {
                'id': str(t.id),
                'name': t.name,
                'status': t.status,
                'updated_at': t.updated_at.isoformat()
            } for t in recent_templates
        ],
        'recent_generations': [
            {
                'id': str(g.id),
                'language': g.language,
                'status': g.status,
                'created_at': g.created_at.isoformat()
            } for g in recent_generations
        ]
    })
