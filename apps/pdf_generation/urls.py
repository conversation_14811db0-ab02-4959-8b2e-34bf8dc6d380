"""
URL configuration for PDF generation app.
"""

from django.urls import path, include
from . import views

app_name = 'pdf_generation'

# Web interface URLs
web_urlpatterns = [
    path('', views.TemplateListView.as_view(), name='template_list'),
    path('create/', views.TemplateCreateView.as_view(), name='template_create'),
    # Support both UUID and slug for template detail (UUID for backward compatibility)
    path('template/<uuid:pk>/', views.TemplateDetailView.as_view(), name='template_detail_uuid'),
    path('template/<slug:slug>/', views.TemplateDetailView.as_view(), name='template_detail'),
    # Support both UUID and slug for template preview
    path('template/<uuid:template_id>/preview/', views.template_preview_view, name='template_preview_uuid'),
    path('template/<slug:template_slug>/preview/', views.template_preview_view, name='template_preview'),
    path('download/code/<uuid:generated_code_id>/', views.download_generated_code, name='download_code'),
    path('download/preview/<uuid:preview_id>/', views.download_preview_pdf, name='download_preview'),
]

# API URLs
api_urlpatterns = [
    path('template/<uuid:template_id>/generate-preview/', views.generate_preview_api, name='generate_preview_api'),
    path('template/<uuid:template_id>/generate-code/', views.generate_code_api, name='generate_code_api'),
    path('template/<uuid:template_id>/validate/', views.template_validation_api, name='template_validation_api'),
    path('preview/<uuid:preview_id>/status/', views.preview_status_api, name='preview_status_api'),
    path('code/<uuid:generated_code_id>/status/', views.code_generation_status_api, name='code_status_api'),
    path('languages/', views.supported_languages_api, name='supported_languages_api'),
    path('dashboard/stats/', views.dashboard_stats_api, name='dashboard_stats_api'),
]

urlpatterns = [
    path('', include(web_urlpatterns)),
    path('api/', include(api_urlpatterns)),
]
