<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Builder Lazy Loading Test</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9fafb;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: #3b82f6;
            color: white;
            padding: 20px;
        }
        
        .test-content {
            padding: 20px;
        }
        
        .section-test {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
        }
        
        .section-test h3 {
            margin: 0 0 10px 0;
            color: #374151;
        }
        
        .loading {
            color: #f59e0b;
        }
        
        .loaded {
            color: #10b981;
        }
        
        .error {
            color: #ef4444;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        
        .test-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .log {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>Form Builder Lazy Loading Test</h1>
            <p>This page tests the lazy loading implementation for the form builder components.</p>
        </div>
        
        <div class="test-content">
            <div id="test-controls">
                <button class="test-button" onclick="runAllTests()">Run All Tests</button>
                <button class="test-button" onclick="clearLog()">Clear Log</button>
                <button class="test-button" onclick="testSpecificSection('canvas')">Test Canvas</button>
                <button class="test-button" onclick="testSpecificSection('palette')">Test Palette</button>
                <button class="test-button" onclick="testSpecificSection('properties')">Test Properties</button>
            </div>
            
            <div class="section-test">
                <h3>Test Log</h3>
                <div id="test-log" class="log">Ready to run tests...\n</div>
            </div>
            
            <div class="section-test">
                <h3>Section Status</h3>
                <div id="section-status">
                    <div id="canvas-status">Canvas: <span class="status">Not loaded</span></div>
                    <div id="palette-status">Palette: <span class="status">Not loaded</span></div>
                    <div id="properties-status">Properties: <span class="status">Not loaded</span></div>
                    <div id="preview-status">Preview: <span class="status">Not loaded</span></div>
                    <div id="customization-status">Customization: <span class="status">Not loaded</span></div>
                    <div id="settings-status">Settings: <span class="status">Not loaded</span></div>
                    <div id="templates-status">Templates: <span class="status">Not loaded</span></div>
                </div>
            </div>
            
            <div class="section-test">
                <h3>Lazy Loading Demo</h3>
                <div id="lazy-demo">
                    <!-- This will be populated by Vue components -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Mock form builder data for testing
        window.formBuilderData = {
            formData: {
                id: 'test-form',
                name: 'Test Form',
                description: 'A test form for lazy loading',
                slug: 'test-form',
                status: 'draft',
                fields: [],
                settings: {},
                customization: {}
            },
            csrfToken: 'test-token'
        }

        // Simple lazy loader mock for testing
        class MockFormBuilderLazyLoader {
            constructor() {
                this.loadedSections = new Set()
                this.loadingPromises = new Map()
                this.errorStates = new Map()
            }

            async loadSection(sectionName) {
                if (this.loadedSections.has(sectionName)) {
                    return true
                }

                if (this.loadingPromises.has(sectionName)) {
                    return this.loadingPromises.get(sectionName)
                }

                log(`Loading section: ${sectionName}`)
                updateSectionStatus(sectionName, 'loading')
                
                const loadPromise = this._mockLoadSection(sectionName)
                    .then(() => {
                        this.loadedSections.add(sectionName)
                        this.errorStates.delete(sectionName)
                        log(`Successfully loaded section: ${sectionName}`)
                        updateSectionStatus(sectionName, 'loaded')
                        return true
                    })
                    .catch(error => {
                        log(`Failed to load section ${sectionName}: ${error.message}`)
                        this.errorStates.set(sectionName, error)
                        updateSectionStatus(sectionName, 'error')
                        return false
                    })
                    .finally(() => {
                        this.loadingPromises.delete(sectionName)
                    })

                this.loadingPromises.set(sectionName, loadPromise)
                return loadPromise
            }

            async _mockLoadSection(sectionName) {
                // Simulate loading time
                await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500))
                
                // Simulate occasional failures
                if (Math.random() < 0.1) {
                    throw new Error(`Mock error loading ${sectionName}`)
                }
                
                return true
            }

            getSectionError(sectionName) {
                return this.errorStates.get(sectionName)
            }

            isSectionLoaded(sectionName) {
                return this.loadedSections.has(sectionName)
            }

            isSectionLoading(sectionName) {
                return this.loadingPromises.has(sectionName)
            }
        }

        // Initialize mock lazy loader
        window.formBuilderLazyLoader = new MockFormBuilderLazyLoader()

        // Utility functions
        function log(message) {
            const logElement = document.getElementById('test-log')
            const timestamp = new Date().toLocaleTimeString()
            logElement.textContent += `[${timestamp}] ${message}\n`
            logElement.scrollTop = logElement.scrollHeight
        }

        function clearLog() {
            document.getElementById('test-log').textContent = 'Log cleared...\n'
        }

        function updateSectionStatus(sectionName, status) {
            const statusElement = document.getElementById(`${sectionName}-status`)
            if (statusElement) {
                const span = statusElement.querySelector('.status')
                span.textContent = status
                span.className = `status ${status}`
            }
        }

        async function testSpecificSection(sectionName) {
            log(`Testing section: ${sectionName}`)
            try {
                const result = await window.formBuilderLazyLoader.loadSection(sectionName)
                if (result) {
                    log(`✅ Section ${sectionName} test passed`)
                } else {
                    log(`❌ Section ${sectionName} test failed`)
                }
            } catch (error) {
                log(`❌ Section ${sectionName} test error: ${error.message}`)
            }
        }

        async function runAllTests() {
            log('🧪 Starting comprehensive lazy loading tests...')
            
            const sections = ['canvas', 'palette', 'properties', 'preview', 'customization', 'settings', 'templates']
            let passed = 0
            let failed = 0
            
            for (const section of sections) {
                try {
                    const result = await window.formBuilderLazyLoader.loadSection(section)
                    if (result) {
                        passed++
                        log(`✅ ${section} test passed`)
                    } else {
                        failed++
                        log(`❌ ${section} test failed`)
                    }
                } catch (error) {
                    failed++
                    log(`❌ ${section} test error: ${error.message}`)
                }
                
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 100))
            }
            
            log(`\n📊 Test Results: ${passed} passed, ${failed} failed`)
            log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`)
            
            if (failed === 0) {
                log('🎉 All tests passed!')
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            log('Page loaded. Ready to test lazy loading implementation.')
            log('Click "Run All Tests" to start testing.')
        })
    </script>
</body>
</html>
