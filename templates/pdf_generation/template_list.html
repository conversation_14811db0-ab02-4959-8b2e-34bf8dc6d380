{% extends "pdf_generation/base_pdf.html" %}
{% load static %}

{% block title %}PDF Templates - PDFlex{% endblock %}

{% block pdf_title %}PDF Templates{% endblock %}
{% block pdf_subtitle %}Manage your PDF generation templates{% endblock %}

{% block pdf_actions %}
  <a href="{% url 'pdf_generation:template_create' %}"
     class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
    </svg>
    New Template
  </a>
{% endblock %}

{% block pdf_content %}
<div class="max-w-7xl mx-auto px-6 py-6">

  <!-- Search and Filters -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <div class="flex flex-col sm:flex-row gap-4">
      <div class="flex-1">
        <form method="get" class="flex">
          <div class="relative flex-1">
            <svg class="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <input type="text" name="search" value="{{ search }}"
                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                   placeholder="Search templates...">
          </div>
          <button type="submit"
                  class="px-4 py-2 bg-gray-50 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
            <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </button>
        </form>
      </div>
      <div class="flex space-x-2">
        <form method="get">
          {% if search %}<input type="hidden" name="search" value="{{ search }}">{% endif %}
          <select name="status"
                  class="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  onchange="this.form.submit()">
            <option value="">All Status</option>
            {% for value, label in status_choices %}
            <option value="{{ value }}"{% if status_filter == value %} selected{% endif %}>
              {{ label }}
            </option>
            {% endfor %}
          </select>
        </form>
        <button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
          <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
          </svg>
        </button>
        </div>
    </div>

    <!-- Templates Grid -->
    {% if templates %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for template in templates %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition duration-150">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                            <i class="fa-solid fa-file-pdf text-primary text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-text-primary">{{ template.name }}</h3>
                            <p class="text-sm text-text-secondary">{{ template.page_size }} • {{ template.elements.count }} elements</p>
                        </div>
                    </div>
                    <span class="px-2 py-1 text-xs rounded-full {% if template.status == 'active' %}bg-green-100 text-green-800{% elif template.status == 'draft' %}bg-yellow-100 text-yellow-800{% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ template.get_status_display }}
                    </span>
                </div>

                <p class="text-text-secondary text-sm mb-4">{{ template.description|truncatewords:15 }}</p>

                <div class="grid grid-cols-3 gap-4 mb-4 text-center">
                    <div>
                        <div class="text-lg font-semibold text-text-primary">{{ template.elements.count }}</div>
                        <div class="text-xs text-text-secondary">Elements</div>
                    </div>
                    <div>
                        <div class="text-lg font-semibold text-text-primary">{{ template.usage_count }}</div>
                        <div class="text-xs text-text-secondary">Uses</div>
                    </div>
                    <div>
                        <div class="text-lg font-semibold text-text-primary">{{ template.page_size }}</div>
                        <div class="text-xs text-text-secondary">Size</div>
                    </div>
                </div>

                <div class="flex items-center text-sm text-text-secondary mb-4">
                    <i class="fa-solid fa-clock mr-1"></i>
                    Updated {{ template.updated_at|timesince }} ago
                </div>

                <div class="flex space-x-2">
                    <a href="{% url 'pdf_generation:template_detail' template.pk %}"
                       class="flex-1 px-3 py-2 text-center border border-gray-300 rounded-lg hover:bg-gray-50 transition duration-150 text-sm">
                        <i class="fa-solid fa-eye mr-1"></i> View
                    </a>
                    <a href="{% url 'pdf_generation:template_preview' template.pk %}"
                       class="flex-1 px-3 py-2 text-center bg-green-50 text-green-700 border border-green-200 rounded-lg hover:bg-green-100 transition duration-150 text-sm">
                        <i class="fa-solid fa-play mr-1"></i> Preview
                    </a>
                    <div class="relative">
                        <button type="button" class="px-3 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition duration-150 text-sm dropdown-toggle"
                                onclick="toggleDropdown(this)">
                            <i class="fa-solid fa-code mr-1"></i> Generate
                        </button>
                        <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden dropdown-menu">
                            <a href="#" onclick="generateCode('{{ template.pk }}', 'python')" class="block px-4 py-2 text-sm text-text-primary hover:bg-gray-100">
                                <i class="fa-brands fa-python mr-2 text-blue-500"></i> Python
                            </a>
                            <a href="#" onclick="generateCode('{{ template.pk }}', 'javascript')" class="block px-4 py-2 text-sm text-text-primary hover:bg-gray-100">
                                <i class="fa-brands fa-js mr-2 text-yellow-500"></i> JavaScript
                            </a>
                            <a href="#" onclick="generateCode('{{ template.pk }}', 'php')" class="block px-4 py-2 text-sm text-text-primary hover:bg-gray-100">
                                <i class="fa-brands fa-php mr-2 text-purple-500"></i> PHP
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="flex items-center justify-between mt-8">
        <div class="text-sm text-text-secondary">
            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} templates
        </div>
        <nav class="flex items-center space-x-2">
            {% if page_obj.has_previous %}
            <a href="?page=1{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}"
               class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition duration-150">
                First
            </a>
            <a href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}"
               class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition duration-150">
                <i class="fa-solid fa-chevron-left mr-1"></i> Previous
            </a>
            {% endif %}

            <span class="px-3 py-2 text-sm bg-primary text-white rounded-lg">
                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
            </span>

            {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}"
               class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition duration-150">
                Next <i class="fa-solid fa-chevron-right ml-1"></i>
            </a>
            <a href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}"
               class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition duration-150">
                Last
            </a>
            {% endif %}
        </nav>
    </div>
    {% endif %}

    {% else %}
    <!-- Empty State -->
    <div class="text-center py-16">
        <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-gray-100 mb-6">
            <i class="fa-solid fa-file-pdf text-gray-400 text-4xl"></i>
        </div>
        <h3 class="text-xl font-semibold text-text-primary mb-2">No Templates Found</h3>
        <p class="text-text-secondary mb-6 max-w-md mx-auto">
            {% if search or status_filter %}
                No templates match your search criteria. <a href="{% url 'pdf_generation:template_list' %}" class="text-primary hover:text-primary-hover">Clear filters</a>
            {% else %}
                Get started by creating your first PDF template to generate professional documents.
            {% endif %}
        </p>
        {% if not search and not status_filter %}
        <a href="{% url 'forms:form_list' %}" class="bg-primary hover:bg-primary-hover text-white px-6 py-3 rounded-lg transition duration-150 inline-flex items-center">
            <i class="fa-solid fa-plus mr-2"></i> Create Your First Template
        </a>
        {% endif %}
    </div>
    {% endif %}
</div>

<!-- Code Generation Modal -->
<div id="codeGenerationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-text-primary">Generate Code</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal()">
                <i class="fa-solid fa-times"></i>
            </button>
        </div>

        <div class="mb-4">
            <div id="generationStatus" class="flex items-center">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                <span class="text-text-secondary">Starting code generation...</span>
            </div>

            <div id="generationResult" class="hidden">
                <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-4">
                    <i class="fa-solid fa-check-circle mr-2"></i>
                    Code generated successfully!
                </div>
                <button id="downloadCode" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition duration-150">
                    <i class="fa-solid fa-download mr-2"></i>
                    Download Code Package
                </button>
            </div>

            <div id="generationError" class="hidden">
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <i class="fa-solid fa-exclamation-circle mr-2"></i>
                    <span id="errorMessage"></span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Dropdown functionality
function toggleDropdown(button) {
    const dropdown = button.nextElementSibling;
    const isHidden = dropdown.classList.contains('hidden');

    // Close all other dropdowns
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
        menu.classList.add('hidden');
    });

    // Toggle current dropdown
    if (isHidden) {
        dropdown.classList.remove('hidden');
    }
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('.dropdown-toggle')) {
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.classList.add('hidden');
        });
    }
});

// Modal functionality
function closeModal() {
    document.getElementById('codeGenerationModal').classList.add('hidden');
}

function generateCode(templateId, language) {
    const modal = document.getElementById('codeGenerationModal');
    modal.classList.remove('hidden');

    // Reset modal state
    document.getElementById('generationStatus').classList.remove('hidden');
    document.getElementById('generationResult').classList.add('hidden');
    document.getElementById('generationError').classList.add('hidden');

    // Start code generation
    fetch(`/pdf-generation/api/template/${templateId}/generate-code/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            language: language,
            options: {}
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Poll for completion (simplified)
            setTimeout(() => {
                document.getElementById('generationStatus').classList.add('hidden');
                document.getElementById('generationResult').classList.remove('hidden');

                // Set up download button
                document.getElementById('downloadCode').onclick = () => {
                    window.location.href = `/pdf-generation/download/code/${data.task_id}/`;
                };
            }, 3000);
        } else {
            showGenerationError(data.error || 'Unknown error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showGenerationError('Network error occurred');
    });
}

function showGenerationError(message) {
    document.getElementById('generationStatus').classList.add('hidden');
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('generationError').classList.remove('hidden');
}

// Search functionality
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit search after typing stops
    let searchTimeout;
    const searchInput = document.querySelector('input[name="search"]');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    }
});
</script>
{% endblock %}
