{% extends 'base.html' %}
{% load static %}

{% comment %}
Base template for all PDF generation related pages.
Provides consistent structure, styling, and functionality patterns.
{% endcomment %}

{% block extra_css %}
  <!-- PDF Generation Specific CSS -->
  <style>
    /* PDF Template Container */
    .pdf-template-container {
      min-height: calc(100vh - 4rem);
    }
    
    /* PDF Preview Styles */
    .pdf-preview-canvas {
      background: #f8f9fa;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      position: relative;
      overflow: auto;
      min-height: 600px;
    }
    
    .pdf-element {
      position: absolute;
      border: 2px dashed #3b82f6;
      background: rgba(59, 130, 246, 0.1);
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .pdf-element:hover {
      border-color: #1d4ed8;
      background: rgba(59, 130, 246, 0.2);
      transform: scale(1.02);
    }
    
    .pdf-element.selected {
      border-color: #dc2626;
      background: rgba(220, 38, 38, 0.1);
      box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.3);
    }
    
    .element-label {
      position: absolute;
      top: -20px;
      left: 0;
      background: #1f2937;
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
      white-space: nowrap;
    }
    
    /* Code Generation Styles */
    .code-preview {
      background: #1f2937;
      color: #f9fafb;
      border-radius: 8px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 14px;
      line-height: 1.5;
    }
    
    .code-preview pre {
      margin: 0;
      padding: 1rem;
      overflow-x: auto;
    }
    
    /* Status Badges */
    .status-badge {
      @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }
    
    .status-badge.draft {
      @apply bg-yellow-100 text-yellow-800;
    }
    
    .status-badge.active {
      @apply bg-green-100 text-green-800;
    }
    
    .status-badge.archived {
      @apply bg-gray-100 text-gray-800;
    }
    
    .status-badge.deprecated {
      @apply bg-red-100 text-red-800;
    }
    
    /* Animation classes */
    .animate-fade-in {
      animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .animate-slide-in {
      animation: slideIn 0.3s ease-in-out;
    }
    
    @keyframes slideIn {
      from { opacity: 0; transform: translateX(-20px); }
      to { opacity: 1; transform: translateX(0); }
    }
    
    /* Custom scrollbar for panels */
    .pdf-panel-scroll {
      scrollbar-width: thin;
      scrollbar-color: #cbd5e0 #f7fafc;
    }
    
    .pdf-panel-scroll::-webkit-scrollbar {
      width: 6px;
    }
    
    .pdf-panel-scroll::-webkit-scrollbar-track {
      background: #f7fafc;
    }
    
    .pdf-panel-scroll::-webkit-scrollbar-thumb {
      background-color: #cbd5e0;
      border-radius: 3px;
    }
    
    .pdf-panel-scroll::-webkit-scrollbar-thumb:hover {
      background-color: #a0aec0;
    }
  </style>
  
  {% block pdf_extra_css %}{% endblock %}
{% endblock %}

{% block content %}
  <div class="pdf-template-container">
    {% block pdf_header %}
      <!-- Default PDF template header -->
      <div class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <!-- Breadcrumb Navigation -->
            <nav class="flex mb-2" aria-label="Breadcrumb">
              <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                  <a href="{% url 'pdf_generation:template_list' %}" 
                     class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                    </svg>
                    Templates
                  </a>
                </li>
                {% block pdf_breadcrumb %}{% endblock %}
              </ol>
            </nav>
            
            <h1 class="text-2xl font-bold text-gray-900">
              {% block pdf_title %}PDF Template{% endblock %}
            </h1>
            <p class="text-sm text-gray-500">
              {% block pdf_subtitle %}Manage PDF generation templates{% endblock %}
            </p>
          </div>
          <div class="flex space-x-3">
            {% block pdf_actions %}
              <!-- Default actions - can be overridden -->
              <a href="{% url 'pdf_generation:template_list' %}" 
                 class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Templates
              </a>
            {% endblock %}
          </div>
        </div>
      </div>
    {% endblock %}
    
    {% block pdf_content %}
      <!-- Main PDF template content area - must be implemented by child templates -->
    {% endblock %}
    
    {% block pdf_modals %}
      <!-- Modal containers for PDF-related dialogs -->
    {% endblock %}
  </div>
  
  <!-- Fallback content for non-JS users -->
  <div id="pdf-fallback-content" class="hidden min-h-screen bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
      <div class="bg-white p-6 rounded-lg shadow">
        <h1 class="text-2xl font-bold text-gray-900 mb-4">
          {% block pdf_fallback_title %}PDF Template Interface{% endblock %}
        </h1>
        <p class="text-gray-600 mb-4">
          {% block pdf_fallback_message %}JavaScript is required to use this interface.{% endblock %}
        </p>
        <div class="bg-blue-50 p-4 rounded-lg">
          <p class="text-blue-800 font-medium">Template Information:</p>
          {% if template %}
            <p class="text-blue-700 mt-1">Name: {{ template.name }}</p>
            <p class="text-blue-700">Status: {{ template.status }}</p>
            <p class="text-blue-700">ID: {{ template.id }}</p>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  {% block pdf_data_scripts %}
    <!-- PDF template data initialization scripts -->
  {% endblock %}
  
  {% block pdf_vue_scripts %}
    <!-- Vue.js and PDF-specific JavaScript -->
  {% endblock %}
  
  {% block pdf_extra_js %}
    <!-- Additional PDF-specific JavaScript -->
  {% endblock %}
{% endblock %}
