{% extends "pdf_generation/base_pdf.html" %}
{% load static %}

{% block title %}Create New Template - PDFlex{% endblock %}

{% block pdf_title %}Create New Template{% endblock %}
{% block pdf_subtitle %}Design a new PDF template for your documents{% endblock %}

{% block pdf_breadcrumb %}
  <li>
    <div class="flex items-center">
      <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
      </svg>
      <span class="ml-1 text-sm font-medium text-gray-500">Create Template</span>
    </div>
  </li>
{% endblock %}

{% block pdf_content %}
<div class="max-w-4xl mx-auto px-6 py-6">
  <form method="post" class="space-y-8">
    {% csrf_token %}
    
    <!-- Basic Information -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            Template Name *
          </label>
          {{ form.name }}
          {% if form.name.errors %}
            <div class="mt-1 text-sm text-red-600">
              {{ form.name.errors.0 }}
            </div>
          {% endif %}
        </div>
        
        <div>
          <label for="{{ form.organization.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            Organization
          </label>
          {{ form.organization }}
          {% if form.organization.errors %}
            <div class="mt-1 text-sm text-red-600">
              {{ form.organization.errors.0 }}
            </div>
          {% endif %}
        </div>
      </div>
      
      <div class="mt-6">
        <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
          Description
        </label>
        {{ form.description }}
        {% if form.description.errors %}
          <div class="mt-1 text-sm text-red-600">
            {{ form.description.errors.0 }}
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Page Configuration -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Page Configuration</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label for="{{ form.page_size.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            Page Size
          </label>
          {{ form.page_size }}
          {% if form.page_size.errors %}
            <div class="mt-1 text-sm text-red-600">
              {{ form.page_size.errors.0 }}
            </div>
          {% endif %}
        </div>
        
        <div>
          <label for="{{ form.orientation.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            Orientation
          </label>
          {{ form.orientation }}
          {% if form.orientation.errors %}
            <div class="mt-1 text-sm text-red-600">
              {{ form.orientation.errors.0 }}
            </div>
          {% endif %}
        </div>
      </div>
      
      <!-- Custom Dimensions (shown when page_size is CUSTOM) -->
      <div id="custom-dimensions" class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6" style="display: none;">
        <div>
          <label for="{{ form.custom_width.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            Custom Width (mm)
          </label>
          {{ form.custom_width }}
          {% if form.custom_width.errors %}
            <div class="mt-1 text-sm text-red-600">
              {{ form.custom_width.errors.0 }}
            </div>
          {% endif %}
        </div>
        
        <div>
          <label for="{{ form.custom_height.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            Custom Height (mm)
          </label>
          {{ form.custom_height }}
          {% if form.custom_height.errors %}
            <div class="mt-1 text-sm text-red-600">
              {{ form.custom_height.errors.0 }}
            </div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Margins -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Margins (mm)</h3>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div>
          <label for="{{ form.margin_top.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            Top
          </label>
          {{ form.margin_top }}
          {% if form.margin_top.errors %}
            <div class="mt-1 text-sm text-red-600">
              {{ form.margin_top.errors.0 }}
            </div>
          {% endif %}
        </div>
        
        <div>
          <label for="{{ form.margin_bottom.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            Bottom
          </label>
          {{ form.margin_bottom }}
          {% if form.margin_bottom.errors %}
            <div class="mt-1 text-sm text-red-600">
              {{ form.margin_bottom.errors.0 }}
            </div>
          {% endif %}
        </div>
        
        <div>
          <label for="{{ form.margin_left.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            Left
          </label>
          {{ form.margin_left }}
          {% if form.margin_left.errors %}
            <div class="mt-1 text-sm text-red-600">
              {{ form.margin_left.errors.0 }}
            </div>
          {% endif %}
        </div>
        
        <div>
          <label for="{{ form.margin_right.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            Right
          </label>
          {{ form.margin_right }}
          {% if form.margin_right.errors %}
            <div class="mt-1 text-sm text-red-600">
              {{ form.margin_right.errors.0 }}
            </div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Template Inheritance -->
    {% if parent_templates %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Template Inheritance</h3>
      <div>
        <label for="{{ form.parent_template.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
          Parent Template (Optional)
        </label>
        {{ form.parent_template }}
        <p class="mt-2 text-sm text-gray-500">
          Select a parent template to inherit elements and settings from.
        </p>
        {% if form.parent_template.errors %}
          <div class="mt-1 text-sm text-red-600">
            {{ form.parent_template.errors.0 }}
          </div>
        {% endif %}
      </div>
    </div>
    {% endif %}

    <!-- Form Actions -->
    <div class="flex justify-end space-x-4">
      <a href="{% url 'pdf_generation:template_list' %}"
         class="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
        Cancel
      </a>
      <button type="submit"
              class="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
        Create Template
      </button>
    </div>
  </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const pageSizeSelect = document.getElementById('{{ form.page_size.id_for_label }}');
  const customDimensions = document.getElementById('custom-dimensions');
  
  function toggleCustomDimensions() {
    if (pageSizeSelect.value === 'CUSTOM') {
      customDimensions.style.display = 'block';
    } else {
      customDimensions.style.display = 'none';
    }
  }
  
  // Initial check
  toggleCustomDimensions();
  
  // Listen for changes
  pageSizeSelect.addEventListener('change', toggleCustomDimensions);
});
</script>
{% endblock %}
