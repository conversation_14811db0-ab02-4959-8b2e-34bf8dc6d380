{% extends "pdf_generation/base_pdf.html" %}
{% load static %}

{% block title %}Preview Template - {{ template.name }} - PDFlex{% endblock %}

{% block pdf_breadcrumb %}
  <li>
    <div class="flex items-center">
      <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
      </svg>
      <a href="{% url 'pdf_generation:template_detail' template.slug %}" class="ml-1 text-sm font-medium text-gray-500 hover:text-gray-700">{{ template.name }}</a>
    </div>
  </li>
  <li>
    <div class="flex items-center">
      <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
      </svg>
      <span class="ml-1 text-sm font-medium text-gray-500">Preview</span>
    </div>
  </li>
{% endblock %}

{% block pdf_title %}{{ template.name }} - Preview{% endblock %}
{% block pdf_subtitle %}Interactive template preview with live data editing{% endblock %}

{% block pdf_actions %}
  <button type="button"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          onclick="window.location.href='{% url 'pdf_generation:template_detail' template.slug %}'">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
    </svg>
    Back to Template
  </button>

  <button type="button"
          id="refreshPreview"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
    </svg>
    Refresh Preview
  </button>
{% endblock %}

{% block pdf_extra_css %}
<style>
    .preview-container {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: 24px;
        height: calc(100vh - 200px);
        min-height: 600px;
    }

    .preview-canvas {
        background: #f8f9fa;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        position: relative;
        overflow: auto;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .preview-sidebar {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 24px;
        overflow-y: auto;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
        top: -20px;
        left: 0;
        background: #007cba;
        color: white;
        padding: 2px 6px;
        font-size: 10px;
        border-radius: 3px;
    }
    
    .data-editor {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        line-height: 1.4;
    }
    
    .preview-controls {
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 6px;
    }
    
    .status-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-pending { background-color: #ffc107; }
    .status-generating { background-color: #17a2b8; }
    .status-completed { background-color: #28a745; }
    .status-failed { background-color: #dc3545; }
    
    .loading-spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #007cba;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block pdf_content %}
<div class="max-w-7xl mx-auto px-6 py-6">
    <div class="preview-container">
        <!-- Preview Canvas -->
        <div class="pdf-preview-canvas" id="previewCanvas">
            <div class="preview-page bg-white mx-auto relative shadow-lg"
                 id="previewPage"
                 style="width: 210mm; height: 297mm; margin: 20px auto;">

                <!-- Loading State -->
                <div id="previewLoading" class="absolute inset-0 hidden items-center justify-center bg-white bg-opacity-75">
                    <div class="text-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                        <p class="text-gray-600">Generating preview...</p>
                    </div>
                </div>

                <!-- Template Elements -->
                {% for element in elements %}
                <div class="pdf-element"
                     data-element-id="{{ element.id }}"
                     data-element-type="{{ element.element_type }}"
                     style="left: {{ element.x }}mm; top: {{ element.y }}mm; width: {{ element.width|default:50 }}mm; height: {{ element.height|default:20 }}mm;">
                    <div class="element-label">{{ element.name }}</div>
                    {% if element.element_type == 'text' %}
                        <div class="p-1 text-xs overflow-hidden">
                            {{ element.text_properties.content|default:"Text content" }}
                        </div>
                    {% elif element.element_type == 'image' %}
                        <div class="flex items-center justify-center h-full text-gray-500">
                            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Image
                        </div>
                    {% elif element.element_type == 'table' %}
                        <div class="flex items-center justify-center h-full text-gray-500">
                            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0V4a1 1 0 011-1h16a1 1 0 011 1v16a1 1 0 01-1 1H4a1 1 0 01-1-1V10z"></path>
                            </svg>
                            Table
                        </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- Preview Sidebar -->
        <div class="preview-sidebar pdf-panel-scroll">
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Preview Controls</h3>
                <div class="space-y-3">
                    <button id="generatePreview"
                            class="w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                        <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10V7a2 2 0 01-2 2H9a2 2 0 01-2-2V4a2 2 0 012-2h8a2 2 0 012 2z"></path>
                        </svg>
                        Generate Preview
                    </button>
                    <button id="downloadPreview"
                            class="w-full px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors hidden">
                        <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Download PDF
                    </button>

                    <div id="previewStatus" class="hidden">
                        <div class="flex items-center p-3 bg-blue-50 rounded-md">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                            <span id="statusText" class="text-sm text-blue-800">Ready</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="border-t border-gray-200 pt-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Sample Data</h3>
                <p class="text-sm text-gray-500 mb-4">Edit the sample data below to customize the preview:</p>
                <textarea id="sampleDataEditor"
                          class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500 font-mono"
                          rows="15">{{ sample_data }}</textarea>
                <button id="updateData"
                        class="w-full mt-3 px-4 py-2 text-sm font-medium text-blue-600 bg-white border border-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Update Preview Data
                </button>
            </div>

            <div id="elementDetails" class="border-t border-gray-200 pt-6 hidden">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Element Details</h3>
                <div id="elementInfo" class="bg-gray-50 rounded-lg p-4"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block pdf_extra_js %}
<script>
class TemplatePreview {
    constructor() {
        this.templateId = '{{ template.id }}';
        this.currentPreviewId = null;
        this.selectedElement = null;
        this.pollInterval = null;
        
        this.initializeEventListeners();
    }
    
    initializeEventListeners() {
        // Generate preview button
        document.getElementById('generatePreview').addEventListener('click', () => {
            this.generatePreview();
        });
        
        // Update data button
        document.getElementById('updateData').addEventListener('click', () => {
            this.updatePreviewData();
        });
        
        // Element selection
        document.querySelectorAll('.template-element').forEach(element => {
            element.addEventListener('click', (e) => {
                this.selectElement(e.currentTarget);
            });
        });
        
        // Download preview button
        document.getElementById('downloadPreview').addEventListener('click', () => {
            this.downloadPreview();
        });
    }
    
    generatePreview() {
        const sampleData = this.getSampleData();
        if (!sampleData) return;
        
        this.updateStatus('generating', 'Generating preview...');
        
        fetch(`/pdf-generation/api/template/${this.templateId}/generate-preview/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCSRFToken()
            },
            body: JSON.stringify({
                sample_data: sampleData
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.pollPreviewStatus(data.task_id);
            } else {
                this.updateStatus('failed', 'Failed to start preview generation');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.updateStatus('failed', 'Error starting preview generation');
        });
    }
    
    pollPreviewStatus(taskId) {
        // In a real implementation, you'd poll the task status
        // For now, we'll simulate the process
        setTimeout(() => {
            this.updateStatus('completed', 'Preview generated successfully');
            document.getElementById('downloadPreview').style.display = 'inline-block';
        }, 3000);
    }
    
    updatePreviewData() {
        const sampleData = this.getSampleData();
        if (sampleData) {
            // Update the preview canvas with new data
            this.updateStatus('pending', 'Data updated - click Generate Preview to see changes');
            document.getElementById('downloadPreview').style.display = 'none';
        }
    }
    
    selectElement(element) {
        // Remove previous selection
        document.querySelectorAll('.template-element.selected').forEach(el => {
            el.classList.remove('selected');
        });
        
        // Select new element
        element.classList.add('selected');
        this.selectedElement = element;
        
        // Show element details
        this.showElementDetails(element);
    }
    
    showElementDetails(element) {
        const elementId = element.dataset.elementId;
        const elementType = element.dataset.elementType;
        const elementName = element.querySelector('.element-label').textContent;
        
        const detailsDiv = document.getElementById('elementDetails');
        const infoDiv = document.getElementById('elementInfo');
        
        infoDiv.innerHTML = `
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">${elementName}</h6>
                    <p class="card-text">
                        <strong>Type:</strong> ${elementType}<br>
                        <strong>ID:</strong> ${elementId}<br>
                        <strong>Position:</strong> ${element.style.left}, ${element.style.top}<br>
                        <strong>Size:</strong> ${element.style.width} × ${element.style.height}
                    </p>
                </div>
            </div>
        `;
        
        detailsDiv.style.display = 'block';
    }
    
    getSampleData() {
        try {
            const dataText = document.getElementById('sampleDataEditor').value;
            return JSON.parse(dataText);
        } catch (error) {
            alert('Invalid JSON in sample data. Please check your syntax.');
            return null;
        }
    }
    
    updateStatus(status, message) {
        const statusDiv = document.getElementById('previewStatus');
        const statusIndicator = statusDiv.querySelector('.status-indicator');
        const statusText = document.getElementById('statusText');
        
        statusIndicator.className = `status-indicator status-${status}`;
        statusText.textContent = message;
        statusDiv.style.display = 'block';
        
        if (status === 'generating') {
            statusText.innerHTML = `<span class="loading-spinner"></span> ${message}`;
        }
    }
    
    downloadPreview() {
        if (this.currentPreviewId) {
            window.location.href = `/pdf-generation/download/preview/${this.currentPreviewId}/`;
        }
    }
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]').value;
    }
}

// Initialize preview when page loads
document.addEventListener('DOMContentLoaded', () => {
    new TemplatePreview();
});
</script>
{% endblock %}
