{% extends "pdf_generation/base_pdf.html" %}
{% load static %}

{% block title %}{{ template.name }} - Template Details - PDFlex{% endblock %}

{% block pdf_breadcrumb %}
  <li>
    <div class="flex items-center">
      <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
      </svg>
      <span class="ml-1 text-sm font-medium text-gray-500">{{ template.name }}</span>
    </div>
  </li>
{% endblock %}

{% block pdf_title %}{{ template.name }}{% endblock %}
{% block pdf_subtitle %}{{ template.description }}{% endblock %}

{% block pdf_actions %}
  <div class="relative inline-block text-left">
    <button type="button"
            class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
            onclick="window.location.href='{% url 'pdf_generation:template_preview' template.slug %}'">
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10V7a2 2 0 01-2 2H9a2 2 0 01-2-2V4a2 2 0 012-2h8a2 2 0 012 2z"></path>
      </svg>
      Preview
    </button>
  </div>

  <div class="relative inline-block text-left" x-data="{ open: false }">
    <button type="button"
            class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            @click="open = !open">
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
      </svg>
      Generate Code
      <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </button>

    <div x-show="open"
         @click.away="open = false"
         x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
      <div class="py-1">
        <a href="#" onclick="generateCode('{{ template.pk }}', 'python')"
           class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
          <i class="fab fa-python mr-2 text-blue-500"></i> Python
        </a>
        <a href="#" onclick="generateCode('{{ template.pk }}', 'javascript')"
           class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
          <i class="fab fa-js mr-2 text-yellow-500"></i> JavaScript
        </a>
        <a href="#" onclick="generateCode('{{ template.pk }}', 'php')"
           class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
          <i class="fab fa-php mr-2 text-purple-500"></i> PHP
        </a>
      </div>
    </div>
  </div>
{% endblock %}

{% block pdf_content %}
<!-- Template Overview Cards -->
<div class="max-w-7xl mx-auto px-6 py-6">
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Elements Count Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="text-center">
        <h3 class="text-sm font-medium text-gray-500 mb-2">Elements</h3>
        <div class="text-3xl font-bold text-blue-600">{{ template.elements.count }}</div>
      </div>
    </div>

    <!-- Usage Count Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="text-center">
        <h3 class="text-sm font-medium text-gray-500 mb-2">Times Used</h3>
        <div class="text-3xl font-bold text-green-600">{{ template.usage_count|default:0 }}</div>
      </div>
    </div>

    <!-- Generated Codes Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="text-center">
        <h3 class="text-sm font-medium text-gray-500 mb-2">Generated Codes</h3>
        <div class="text-3xl font-bold text-purple-600">{{ generated_codes|length|default:0 }}</div>
      </div>
    </div>

    <!-- Previews Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="text-center">
        <h3 class="text-sm font-medium text-gray-500 mb-2">Previews</h3>
        <div class="text-3xl font-bold text-orange-600">{{ previews|length|default:0 }}</div>
      </div>
    </div>
  </div>
  <!-- Main Content Tabs -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs" x-data="{ activeTab: 'overview' }">
        <button @click="activeTab = 'overview'"
                :class="activeTab === 'overview' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors">
          <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Overview
        </button>

        <button @click="activeTab = 'elements'"
                :class="activeTab === 'elements' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors">
          <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
          </svg>
          Elements
        </button>

        <button @click="activeTab = 'generated'"
                :class="activeTab === 'generated' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors">
          <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
          </svg>
          Generated Code
        </button>

        <button @click="activeTab = 'previews'"
                :class="activeTab === 'previews' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors">
          <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
          Previews
        </button>

        <button @click="activeTab = 'versions'"
                :class="activeTab === 'versions' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors">
          <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Versions
        </button>
      </nav>
    </div>
    
    <div class="tab-content" id="templateTabsContent">
        <!-- Overview Tab -->
        <div class="tab-pane fade show active" id="overview" role="tabpanel">
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Template Configuration</h6>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-4">Status:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-{% if template.status == 'active' %}success{% elif template.status == 'draft' %}warning{% else %}secondary{% endif %}">
                                        {{ template.get_status_display }}
                                    </span>
                                </dd>
                                
                                <dt class="col-sm-4">Page Size:</dt>
                                <dd class="col-sm-8">{{ template.get_page_size_display }}</dd>
                                
                                <dt class="col-sm-4">Orientation:</dt>
                                <dd class="col-sm-8">{{ template.get_orientation_display }}</dd>
                                
                                <dt class="col-sm-4">Version:</dt>
                                <dd class="col-sm-8">{{ template.version }}</dd>
                                
                                <dt class="col-sm-4">Created:</dt>
                                <dd class="col-sm-8">{{ template.created_at|date:"M d, Y" }}</dd>
                                
                                <dt class="col-sm-4">Last Updated:</dt>
                                <dd class="col-sm-8">{{ template.updated_at|date:"M d, Y H:i" }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Page Dimensions</h6>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-6">Width:</dt>
                                <dd class="col-sm-6">{{ template.custom_width|default:template.page_size }} mm</dd>
                                
                                <dt class="col-sm-6">Height:</dt>
                                <dd class="col-sm-6">{{ template.custom_height|default:template.page_size }} mm</dd>
                                
                                <dt class="col-sm-6">Top Margin:</dt>
                                <dd class="col-sm-6">{{ template.margin_top }} mm</dd>
                                
                                <dt class="col-sm-6">Bottom Margin:</dt>
                                <dd class="col-sm-6">{{ template.margin_bottom }} mm</dd>
                                
                                <dt class="col-sm-6">Left Margin:</dt>
                                <dd class="col-sm-6">{{ template.margin_left }} mm</dd>
                                
                                <dt class="col-sm-6">Right Margin:</dt>
                                <dd class="col-sm-6">{{ template.margin_right }} mm</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Elements Tab -->
        <div class="tab-pane fade" id="elements" role="tabpanel">
            <div class="mt-4">
                {% if template.elements.exists %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Position</th>
                                <th>Size</th>
                                <th>Visible</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for element in template.elements.all %}
                            <tr>
                                <td>{{ element.name }}</td>
                                <td>
                                    <span class="badge bg-secondary">{{ element.get_element_type_display }}</span>
                                </td>
                                <td>{{ element.x }}, {{ element.y }} mm</td>
                                <td>{{ element.width|default:"auto" }} × {{ element.height|default:"auto" }} mm</td>
                                <td>
                                    {% if element.visible %}
                                        <i class="fas fa-eye text-success"></i>
                                    {% else %}
                                        <i class="fas fa-eye-slash text-muted"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" 
                                            onclick="showElementDetails('{{ element.id }}')">
                                        <i class="fas fa-info"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-puzzle-piece fa-3x text-muted mb-3"></i>
                    <h5>No Elements</h5>
                    <p class="text-muted">This template doesn't have any elements yet.</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Generated Code Tab -->
        <div class="tab-pane fade" id="generated" role="tabpanel">
            <div class="mt-4">
                {% if generated_codes %}
                <div class="row">
                    {% for code in generated_codes %}
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="card-title">
                                            {% if code.language == 'python' %}<i class="fab fa-python"></i>
                                            {% elif code.language == 'javascript' %}<i class="fab fa-js"></i>
                                            {% elif code.language == 'php' %}<i class="fab fa-php"></i>
                                            {% endif %}
                                            {{ code.get_language_display }}
                                        </h6>
                                        <p class="card-text small text-muted">
                                            Generated {{ code.created_at|timesince }} ago
                                        </p>
                                    </div>
                                    <span class="badge bg-{% if code.status == 'completed' %}success{% elif code.status == 'failed' %}danger{% elif code.status == 'generating' %}warning{% else %}secondary{% endif %}">
                                        {{ code.get_status_display }}
                                    </span>
                                </div>
                                
                                {% if code.status == 'completed' %}
                                <div class="mt-2">
                                    <small class="text-muted">
                                        Downloads: {{ code.download_count }} | 
                                        Size: {{ code.code_package.size|filesizeformat }}
                                    </small>
                                </div>
                                <div class="mt-2">
                                    <a href="{% url 'pdf_generation:download_code' code.id %}" 
                                       class="btn btn-sm btn-success">
                                        <i class="fas fa-download"></i> Download
                                    </a>
                                </div>
                                {% elif code.status == 'failed' %}
                                <div class="mt-2">
                                    <small class="text-danger">{{ code.error_message }}</small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-code fa-3x text-muted mb-3"></i>
                    <h5>No Generated Code</h5>
                    <p class="text-muted">Generate code packages for this template.</p>
                    <button class="btn btn-primary" data-bs-toggle="dropdown">
                        <i class="fas fa-plus"></i> Generate Code
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Previews Tab -->
        <div class="tab-pane fade" id="previews" role="tabpanel">
            <div class="mt-4">
                {% if previews %}
                <div class="row">
                    {% for preview in previews %}
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="card-title">Preview</h6>
                                        <p class="card-text small text-muted">
                                            {{ preview.created_at|date:"M d, Y H:i" }}
                                        </p>
                                    </div>
                                    <span class="badge bg-{% if preview.status == 'completed' %}success{% elif preview.status == 'failed' %}danger{% else %}warning{% endif %}">
                                        {{ preview.get_status_display }}
                                    </span>
                                </div>
                                
                                {% if preview.status == 'completed' and preview.preview_pdf %}
                                <div class="mt-2">
                                    <a href="{% url 'pdf_generation:download_preview' preview.id %}" 
                                       class="btn btn-sm btn-success">
                                        <i class="fas fa-download"></i> Download PDF
                                    </a>
                                </div>
                                {% elif preview.status == 'failed' %}
                                <div class="mt-2">
                                    <small class="text-danger">{{ preview.error_message }}</small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-eye fa-3x text-muted mb-3"></i>
                    <h5>No Previews</h5>
                    <p class="text-muted">Generate a preview to see how your template looks.</p>
                    <a href="{% url 'pdf_generation:template_preview' template.pk %}" 
                       class="btn btn-success">
                        <i class="fas fa-play"></i> Generate Preview
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Versions Tab -->
        <div class="tab-pane fade" id="versions" role="tabpanel">
            <div class="mt-4">
                {% if versions %}
                <div class="timeline">
                    {% for version in versions %}
                    <div class="timeline-item">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <h6>Version {{ version.version_number }}</h6>
                            <p class="text-muted">{{ version.get_change_type_display }}</p>
                            {% if version.change_description %}
                            <p>{{ version.change_description }}</p>
                            {% endif %}
                            <small class="text-muted">
                                {{ version.created_at|date:"M d, Y H:i" }} by {{ version.created_by.get_display_name }}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <h5>No Version History</h5>
                    <p class="text-muted">Version history will appear here as you make changes.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #007cba;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #007cba;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}
</style>
  </div>
</div>
{% endblock %}

{% block pdf_extra_js %}
<!-- Alpine.js for interactive components -->
<script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

<script>
function generateCode(templateId, language) {
    console.log(`Generating ${language} code for template ${templateId}`);

    // Show loading state
    const button = event.target.closest('a');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Generating...';
    button.classList.add('opacity-50', 'cursor-not-allowed');

    // Simulate API call
    fetch(`/pdf-generation/api/template/${templateId}/generate-code/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({ language: language })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Redirect to download or show success message
            window.location.href = data.download_url;
        } else {
            alert('Error generating code: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error generating code. Please try again.');
    })
    .finally(() => {
        // Restore button state
        button.innerHTML = originalText;
        button.classList.remove('opacity-50', 'cursor-not-allowed');
    });
}

function showElementDetails(elementId) {
    console.log(`Showing details for element ${elementId}`);
    // Implementation for showing element details
}

// Initialize tooltips and other interactive elements
document.addEventListener('DOMContentLoaded', function() {
    console.log('PDF Template Detail page loaded');
});
</script>
{% endblock %}
