{% comment %}
Form Builder Scripts Component
Initializes the Vue.js form builder application with proper data and configuration.
Integrates with Vite build system and maintains compatibility with Django template inheritance.
{% endcomment %}

{% load static %}
{% load vite_tags %}

<!-- Form Builder Data Initialization -->
<script>
  // Initialize global form builder data for Vue.js application
  window.formBuilderData = {
    // Form data from Django context
    formData: {
      // Basic form information
      id: '{{ form.id }}',
      name: '{{ form.name|escapejs }}',
      description: '{{ form.description|escapejs }}',
      slug: '{{ form.slug|escapejs }}',
      status: '{{ form.status|escapejs }}',
      
      // Form fields (parsed from JSON)
      fields: {{ form_fields_json|safe }},
      
      // Form settings (parsed from JSON)
      settings: {{ form_settings_json|safe }},
      
      // Form customization (parsed from JSON)
      customization: {{ form_customization_json|safe }}
    },
    
    // Configuration for the form builder
    config: {
      formSlug: '{{ form.slug|escapejs }}',
      apiBaseUrl: '/api/forms/',
      csrfToken: '{{ csrf_token }}',
      
      // API endpoints
      endpoints: {
        formUpdate: '/api/forms/{{ form.slug }}/',
        fieldCreate: '/api/forms/{{ form.slug }}/fields/',
        fieldUpdate: '/api/forms/{{ form.slug }}/fields/{id}/',
        fieldDelete: '/api/forms/{{ form.slug }}/fields/{id}/',
        fieldReorder: '/api/forms/{{ form.slug }}/fields/reorder/'
      },
      
      // Feature flags
      features: {
        autosave: true,
        autosaveInterval: 30000, // 30 seconds
        undoRedo: true,
        keyboardShortcuts: true,
        dragAndDrop: true,
        realTimePreview: true
      },
      
      // UI configuration
      ui: {
        sidebarWidth: 320,
        propertiesWidth: 320,
        minCanvasWidth: 400,
        maxUndoSteps: 50
      }
    },
    
    // CSRF token for API requests
    csrfToken: '{{ csrf_token }}'
  };
  
  // Debug logging in development
  {% if debug %}
  console.log('Form Builder Data Initialized:', window.formBuilderData);
  {% endif %}
</script>

<!-- Form Builder Loading Styles -->
<style>
  /* Prevent flickering during initialization */
  #form-builder-app {
    opacity: 1; /* Always visible - let Vue handle loading states */
    transition: opacity 0.3s ease-in-out;
  }

  #form-builder-app.initialized {
    opacity: 1;
  }

  /* Loading state */
  .form-builder-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background-color: #f9fafb;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Ensure proper visibility */
  #form-builder-app.vue-mounted {
    visibility: visible !important;
    opacity: 1 !important;
  }
</style>

<!-- Load Vite Assets for Form Builder -->
{% load_vite_assets 'form-builder' %}

<!-- Form Builder Visibility Control Script -->
<script type="module">
  // Enhanced script to handle loading states, error handling, and progressive enhancement
  console.log('Form Builder visibility controller loaded');

  // Loading progress simulation
  let loadingProgress = 0;
  const progressBar = document.querySelector('.loading-progress');

  function updateLoadingProgress(progress) {
    loadingProgress = Math.min(progress, 100);
    if (progressBar) {
      progressBar.style.width = `${loadingProgress}%`;
    }
  }

  // Simulate loading progress
  const progressInterval = setInterval(() => {
    if (loadingProgress < 90) {
      updateLoadingProgress(loadingProgress + Math.random() * 10);
    }
  }, 200);

  // Function to show the form builder and hide loading
  function showFormBuilder() {
    console.log('Showing form builder...');
    clearInterval(progressInterval);
    updateLoadingProgress(100);

    const formBuilderElement = document.getElementById('form-builder-app');
    const loadingElement = document.getElementById('form-builder-loading');

    if (formBuilderElement) {
      formBuilderElement.classList.add('initialized', 'vue-mounted');
      console.log('Form builder visibility enabled');
    }

    if (loadingElement) {
      setTimeout(() => {
        loadingElement.style.display = 'none';
        console.log('Loading state hidden');
      }, 500); // Small delay for smooth transition
    }
  }

  // Function to show error state
  function showFormBuilderError(error) {
    console.error('Form builder error:', error);
    clearInterval(progressInterval);

    const loadingElement = document.getElementById('form-builder-loading');
    const errorElement = document.getElementById('form-builder-error');

    if (loadingElement) {
      loadingElement.style.display = 'none';
    }

    if (errorElement) {
      errorElement.style.display = 'flex';
      errorElement.classList.remove('hidden');
    }
  }

  // Enhanced error handling
  window.addEventListener('error', (event) => {
    if (event.filename && event.filename.includes('form-builder')) {
      showFormBuilderError(event.error);
    }
  });

  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && event.reason.message && event.reason.message.includes('form-builder')) {
      showFormBuilderError(event.reason);
    }
  });

  // Timeout fallback - if Vue doesn't load within 10 seconds, show error
  const loadingTimeout = setTimeout(() => {
    if (!document.getElementById('form-builder-app').classList.contains('vue-mounted')) {
      showFormBuilderError(new Error('Form builder failed to load within timeout period'));
    }
  }, 10000);

  // Listen for Vue mount success
  document.addEventListener('vue-form-builder-mounted', () => {
    clearTimeout(loadingTimeout);
    showFormBuilder();
  });

  // Listen for Vue mount error
  document.addEventListener('vue-form-builder-error', (event) => {
    clearTimeout(loadingTimeout);
    showFormBuilderError(event.detail);
  });

  // Expose functions globally for Vue to call
  window.showFormBuilder = showFormBuilder;
  window.showFormBuilderError = showFormBuilderError;

  // Monitor for Vue app mounting by checking DOM changes
  function monitorFormBuilderMount() {
    const formBuilderElement = document.getElementById('form-builder-app');

    if (!formBuilderElement) {
      console.error('Form builder element not found');
      return;
    }

    // Use MutationObserver to detect when Vue content is added
    const observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Check if Vue-specific content was added
          for (const node of mutation.addedNodes) {
            if (node.nodeType === Node.ELEMENT_NODE && (
              node.hasAttribute('data-v-') ||
              (node.querySelector && node.querySelector('[data-v-]')) ||
              (node.className && typeof node.className === 'string' &&
               (node.className.includes('form-builder') || node.className.includes('vue')))
            )) {
              console.log('Vue app content detected via MutationObserver');
              observer.disconnect();
              showFormBuilder();
              return;
            }
          }
        }
      }
    });

    observer.observe(formBuilderElement, {
      childList: true,
      subtree: true,
      attributes: false
    });

    // Fallback: Check periodically if Vue app is mounted
    let checkAttempts = 0;
    const maxCheckAttempts = 30; // 3 seconds

    function checkVueMount() {
      checkAttempts++;

      if (formBuilderElement.__vue_app__ ||
          formBuilderElement.querySelector('[data-v-]') ||
          formBuilderElement.children.length > 3) {
        console.log('Vue app detected via periodic check');
        observer.disconnect();
        showFormBuilder();
        return;
      }

      if (checkAttempts < maxCheckAttempts) {
        setTimeout(checkVueMount, 100);
      } else {
        console.log('Vue mount detection timeout, showing form builder anyway');
        observer.disconnect();
        showFormBuilder();
      }
    }

    // Start checking after a short delay
    setTimeout(checkVueMount, 500);
  }

  // Start monitoring when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', monitorFormBuilderMount);
  } else {
    monitorFormBuilderMount();
  }


</script>

<!-- Additional Form Builder Utilities -->
<script>
  // Global utilities for form builder
  window.FormBuilderUtils = {
    // Generate unique field ID
    generateFieldId() {
      return 'field_' + Math.random().toString(36).substr(2, 9);
    },
    
    // Validate field configuration
    validateField(field) {
      if (!field.name || !field.type) {
        return false;
      }
      return true;
    },
    
    // Format field for API
    formatFieldForAPI(field) {
      return {
        name: field.name,
        label: field.label || field.name,
        field_type: field.type,
        required: field.required || false,
        placeholder: field.placeholder || '',
        help_text: field.help_text || '',
        default_value: field.default_value || '',
        order: field.order || 0,
        is_visible: field.is_visible !== false,
        properties: field.properties || {},
        validation_rules: field.validation_rules || {},
        conditional_logic: field.conditional_logic || {}
      };
    },
    
    // Setup CSRF for AJAX requests
    setupCSRF() {
      const csrfToken = window.formBuilderData?.csrfToken;
      if (csrfToken && window.axios) {
        window.axios.defaults.headers.common['X-CSRFToken'] = csrfToken;
      }
    }
  };
  
  // Initialize CSRF setup
  window.FormBuilderUtils.setupCSRF();
  
  {% if debug %}
  console.log('Form Builder utilities loaded:', window.FormBuilderUtils);
  {% endif %}
</script>
