{% comment %}
Form Builder Fallback Component
Static HTML fallback for users without JavaScript or when Vue.js fails to load.
Provides basic form information and links to alternative interfaces.
{% endcomment %}

<div class="form-builder-fallback">
  <div class="max-w-4xl mx-auto p-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-xl font-bold text-gray-900">{{ form.name }}</h2>
          <p class="text-gray-600 mt-1">{{ form.description|default:"No description provided" }}</p>
          <div class="flex items-center mt-2 space-x-4 text-sm text-gray-500">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                         {% if form.status == 'published' %}bg-green-100 text-green-800{% elif form.status == 'draft' %}bg-yellow-100 text-yellow-800{% else %}bg-gray-100 text-gray-800{% endif %}">
              {{ form.status|title }}
            </span>
            <span>{{ form.fields.count }} field{{ form.fields.count|pluralize }}</span>
            <span>Created {{ form.created_at|date:"M d, Y" }}</span>
          </div>
        </div>
        <div class="flex space-x-3">
          <a href="{% url 'forms:form_edit' form.slug %}" 
             class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            Edit Settings
          </a>
          <a href="{% url 'forms:form_detail' form.slug %}" 
             class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
            View Form
          </a>
        </div>
      </div>
    </div>

    <!-- JavaScript Required Notice -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
      <div class="flex items-start">
        <svg class="w-6 h-6 text-blue-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <div>
          <h3 class="text-lg font-semibold text-blue-900 mb-2">Visual Form Builder Requires JavaScript</h3>
          <p class="text-blue-800 mb-3">
            The interactive form builder interface requires JavaScript to function properly. 
            Please enable JavaScript in your browser to access the full form building experience.
          </p>
          <div class="text-sm text-blue-700">
            <p class="mb-2"><strong>With JavaScript enabled, you can:</strong></p>
            <ul class="list-disc list-inside space-y-1 ml-4">
              <li>Drag and drop form fields</li>
              <li>Real-time form preview</li>
              <li>Advanced field customization</li>
              <li>Conditional logic setup</li>
              <li>Form templates and themes</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Current Form Fields -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Current Form Fields</h3>
      
      {% if form.fields.exists %}
        <div class="space-y-4">
          {% for field in form.fields.all %}
            <div class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-3">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    {{ field.field_type|title }}
                  </span>
                  <h4 class="font-medium text-gray-900">{{ field.label }}</h4>
                  {% if field.required %}
                    <span class="text-red-500 text-sm">Required</span>
                  {% endif %}
                </div>
                <span class="text-sm text-gray-500">Order: {{ field.order }}</span>
              </div>
              
              {% if field.help_text %}
                <p class="text-sm text-gray-600 mb-2">{{ field.help_text }}</p>
              {% endif %}
              
              {% if field.placeholder %}
                <p class="text-xs text-gray-500">Placeholder: "{{ field.placeholder }}"</p>
              {% endif %}
              
              <!-- Field-specific properties -->
              {% if field.field_type == 'select' or field.field_type == 'radio' or field.field_type == 'checkbox' %}
                {% if field.options %}
                  <div class="mt-2">
                    <p class="text-xs text-gray-500 mb-1">Options:</p>
                    <div class="flex flex-wrap gap-1">
                      {% for option in field.options %}
                        <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700">
                          {{ option.label }}
                        </span>
                      {% endfor %}
                    </div>
                  </div>
                {% endif %}
              {% endif %}
            </div>
          {% endfor %}
        </div>
      {% else %}
        <div class="text-center py-8">
          <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <h4 class="text-lg font-medium text-gray-900 mb-2">No Fields Added Yet</h4>
          <p class="text-gray-600 mb-4">This form doesn't have any fields yet. Enable JavaScript to start building your form.</p>
        </div>
      {% endif %}
    </div>

    <!-- Alternative Actions -->
    <div class="mt-6 bg-gray-50 rounded-lg p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Alternative Actions</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <a href="{% url 'forms:form_edit' form.slug %}" 
           class="block p-4 bg-white border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <div>
              <h4 class="font-medium text-gray-900">Form Settings</h4>
              <p class="text-sm text-gray-600">Edit basic form properties and settings</p>
            </div>
          </div>
        </a>
        
        <a href="{% url 'forms:form_detail' form.slug %}" 
           class="block p-4 bg-white border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
            <div>
              <h4 class="font-medium text-gray-900">View Form</h4>
              <p class="text-sm text-gray-600">See how your form appears to users</p>
            </div>
          </div>
        </a>
      </div>
    </div>
  </div>
</div>
