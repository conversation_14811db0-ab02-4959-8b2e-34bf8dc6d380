{% extends 'forms/base_form.html' %}
{% load static %}
{% load vite_tags %}

{% block title %}
  Form Builder - {{ form.name }} - PDFlex
{% endblock %}

{% block form_title %}
  {{ form.name }} - Form Builder
{% endblock %}
{% block form_subtitle %}
  Visual Form Designer • {{ form.status }} • {{ form.fields.count }} fields
{% endblock %}

{% block form_actions %}
  <a href="{% url 'forms:form_detail' form.slug %}" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
    </svg>Back to Form
  </a>
  <a href="{% url 'forms:form_edit' form.slug %}" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
    </svg>Settings
  </a>
{% endblock %}

{% block form_content %}
  <!-- Form Builder Loading State -->
  <div id="form-builder-loading" class="form-builder-loading">
    <div class="text-center py-12">
      <div class="loading-spinner mx-auto mb-4"></div>
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Loading Form Builder</h3>
      <p class="text-gray-600 mb-2">Initializing Vue.js components...</p>
      <div class="max-w-md mx-auto">
        <div class="bg-gray-200 rounded-full h-2 mb-4">
          <div class="bg-blue-600 h-2 rounded-full loading-progress" style="width: 0%"></div>
        </div>
        <p class="text-sm text-gray-500">This may take a few moments</p>
      </div>
    </div>
  </div>

  <!-- Form Builder Error State -->
  <div id="form-builder-error" class="form-builder-error hidden">
    <div class="text-center py-12">
      <svg class="w-16 h-16 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
        </path>
      </svg>
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Form Builder Error</h3>
      <p class="text-gray-600 mb-4">There was a problem loading the form builder interface.</p>
      <div class="space-y-2">
        <button onclick="location.reload()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
          Reload Page
        </button>
        <p class="text-sm text-gray-500">If the problem persists, please contact support.</p>
      </div>
    </div>
  </div>

  <!-- Form Builder Vue App Container -->
  <div id="form-builder-app" class="form-builder-app">
    <!-- Fallback content for non-JS users -->
    <noscript>
      <div class="p-8 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 class="text-lg font-semibold text-yellow-800 mb-2">JavaScript Required</h3>
        <p class="text-yellow-700 mb-4">The form builder requires JavaScript to function properly.</p>
        <p class="text-sm text-yellow-600">Please enable JavaScript in your browser and reload the page.</p>
      </div>
    </noscript>

    <!-- Static fallback content (will be replaced by Vue) -->
    <div class="static-fallback">
      {% include 'forms/partials/form_builder_fallback.html' %}
    </div>
  </div>
{% endblock %}

{% block fallback_title %}
  Form Builder
{% endblock %}
{% block fallback_message %}
  JavaScript is required to use the form builder interface.
{% endblock %}

{% block form_vue_scripts %}
  <!-- Include Form Builder JavaScript Components -->
  <!-- Note: Vue.js is loaded via Vite-built assets, not CDN -->
  {% include 'forms/partials/form_builder_scripts.html' %}
  <script>
    document.getElementById('form-builder-app').__vue_app__
  </script>
{% endblock %}
