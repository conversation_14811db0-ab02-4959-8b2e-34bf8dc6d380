{% extends 'base.html' %}
{% load static %}

{% block title %}
  Forms - PDFlex
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-text-primary">Forms</h1>
          <p class="mt-2 text-text-secondary">Create and manage your forms</p>
        </div>
        <div class="flex space-x-3">
          <a href="{% url 'forms:form_create' %}" class="bg-primary hover:bg-primary-hover text-white px-4 py-2 rounded-lg transition duration-150 flex items-center">
            <i class="fa-solid fa-plus mr-2"></i>
            Create Form
          </a>
        </div>
      </div>
    </div>

    <!-- Search and Filter -->
    <div class="mb-6 flex flex-col sm:flex-row gap-4">
      <div class="flex-1">
        <div class="relative">
          <i class="fa-solid fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary"></i>
          <input type="text" placeholder="Search forms..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary" />
        </div>
      </div>
      <div class="flex space-x-2">
        <select class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary">
          <option>All Forms</option>
          <option>Active</option>
          <option>Draft</option>
          <option>Archived</option>
        </select>
        <button class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition duration-150"><i class="fa-solid fa-filter"></i></button>
      </div>
    </div>

    <!-- Forms Grid -->
    <div id="forms-list-app" data-api-base-url="/forms/api">
      <!-- Loading state -->
      <div class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p class="mt-2 text-text-secondary">Loading forms...</p>
      </div>
    </div>
        <!-- Loading placeholders -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="animate-pulse">
            <div class="flex items-center space-x-2 mb-4">
              <div class="w-10 h-10 bg-gray-200 rounded-lg"></div>
              <div>
                <div class="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                <div class="h-3 bg-gray-200 rounded w-20"></div>
              </div>
            </div>
            <div class="h-3 bg-gray-200 rounded w-full mb-4"></div>
            <div class="flex items-center justify-between">
              <div class="flex space-x-4">
                <div class="h-3 bg-gray-200 rounded w-16"></div>
                <div class="h-3 bg-gray-200 rounded w-20"></div>
              </div>
              <div class="h-5 bg-gray-200 rounded w-12"></div>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="animate-pulse">
            <div class="flex items-center space-x-2 mb-4">
              <div class="w-10 h-10 bg-gray-200 rounded-lg"></div>
              <div>
                <div class="h-4 bg-gray-200 rounded w-20 mb-2"></div>
                <div class="h-3 bg-gray-200 rounded w-24"></div>
              </div>
            </div>
            <div class="h-3 bg-gray-200 rounded w-full mb-4"></div>
            <div class="flex items-center justify-between">
              <div class="flex space-x-4">
                <div class="h-3 bg-gray-200 rounded w-14"></div>
                <div class="h-3 bg-gray-200 rounded w-18"></div>
              </div>
              <div class="h-5 bg-gray-200 rounded w-10"></div>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="animate-pulse">
            <div class="flex items-center space-x-2 mb-4">
              <div class="w-10 h-10 bg-gray-200 rounded-lg"></div>
              <div>
                <div class="h-4 bg-gray-200 rounded w-20 mb-2"></div>
                <div class="h-3 bg-gray-200 rounded w-24"></div>
              </div>
            </div>
            <div class="h-3 bg-gray-200 rounded w-full mb-4"></div>
            <div class="flex items-center justify-between">
              <div class="flex space-x-4">
                <div class="h-3 bg-gray-200 rounded w-14"></div>
                <div class="h-3 bg-gray-200 rounded w-18"></div>
              </div>
              <div class="h-5 bg-gray-200 rounded w-10"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div class="text-center py-12 hidden" id="empty-state">
      <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-gray-100 mb-4">
        <i class="fa-solid fa-file-text text-gray-400 text-3xl"></i>
      </div>
      <h3 class="text-lg font-medium text-text-primary mb-2">No forms yet</h3>
      <p class="text-text-secondary mb-6">Get started by creating your first form</p>
      <a href="{% url 'forms:form_create' %}" class="bg-primary hover:bg-primary-hover text-white px-6 py-3 rounded-lg transition duration-150 inline-flex items-center">
        <i class="fa-solid fa-plus mr-2"></i>
        Create Your First Form
      </a>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    // Load and display forms
    document.addEventListener('DOMContentLoaded', async function() {
      const container = document.getElementById('forms-list-app');
      if (!container) return;

      try {
        const response = await fetch('/forms/api/forms/', {
          headers: {
            'Accept': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
          },
          credentials: 'same-origin'
        });

        if (response.ok) {
          const data = await response.json();

          if (data.results && data.results.length > 0) {
            // Render forms grid
            container.innerHTML = `
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                ${data.results.map(form => `
                  <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition duration-150">
                    <div class="p-6">
                      <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-2">
                          <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                            <i class="fa-solid fa-file-text text-primary"></i>
                          </div>
                          <div>
                            <h3 class="font-semibold text-text-primary">${form.name}</h3>
                            <p class="text-sm text-text-secondary">Created ${new Date(form.created_at).toLocaleDateString()}</p>
                          </div>
                        </div>
                        <div class="relative">
                          <a href="/forms/${form.slug}/" class="text-text-secondary hover:text-text-primary">
                            <i class="fa-solid fa-eye"></i>
                          </a>
                        </div>
                      </div>
                      <p class="text-text-secondary text-sm mb-4">${form.description || 'No description provided'}</p>
                      <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4 text-sm text-text-secondary">
                          <span><i class="fa-solid fa-list mr-1"></i> ${form.field_count || 0} fields</span>
                          <span><i class="fa-solid fa-paper-plane mr-1"></i> ${form.submission_count || 0} submissions</span>
                        </div>
                        <span class="px-2 py-1 ${form.status === 'published' ? 'bg-green-100 text-green-800' : form.status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'} text-xs rounded-full">${form.status_display}</span>
                      </div>
                    </div>
                  </div>
                `).join('')}
              </div>
            `;
          } else {
            // Show empty state
            container.innerHTML = `
              <div class="text-center py-12">
                <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-gray-100 mb-4">
                  <i class="fa-solid fa-file-text text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-lg font-medium text-text-primary mb-2">No forms yet</h3>
                <p class="text-text-secondary mb-6">Get started by creating your first form</p>
                <a href="/forms/create/" class="bg-primary hover:bg-primary-hover text-white px-6 py-3 rounded-lg transition duration-150 inline-flex items-center">
                  <i class="fa-solid fa-plus mr-2"></i>
                  Create Your First Form
                </a>
              </div>
            `;
          }
        } else if (response.status === 401 || response.status === 403) {
          // Authentication required
          container.innerHTML = `
            <div class="text-center py-12">
              <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-red-100 mb-4">
                <i class="fa-solid fa-lock text-red-400 text-3xl"></i>
              </div>
              <h3 class="text-lg font-medium text-text-primary mb-2">Authentication Required</h3>
              <p class="text-text-secondary mb-6">Please log in to view your forms</p>
              <a href="/accounts/login/" class="bg-primary hover:bg-primary-hover text-white px-6 py-3 rounded-lg transition duration-150 inline-flex items-center">
                <i class="fa-solid fa-sign-in-alt mr-2"></i>
                Log In
              </a>
            </div>
          `;
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        // Show error state
        container.innerHTML = `
          <div class="text-center py-12">
            <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-red-100 mb-4">
              <i class="fa-solid fa-exclamation-triangle text-red-400 text-3xl"></i>
            </div>
            <h3 class="text-lg font-medium text-text-primary mb-2">Error Loading Forms</h3>
            <p class="text-text-secondary mb-6">There was a problem loading your forms. Please try again.</p>
            <button onclick="location.reload()" class="bg-primary hover:bg-primary-hover text-white px-6 py-3 rounded-lg transition duration-150 inline-flex items-center">
              <i class="fa-solid fa-refresh mr-2"></i>
              Try Again
            </button>
          </div>
        `;
      }
    });
  </script>
  <link rel="stylesheet" href="{% static 'dist/css/main-D7qJ9s9p.css' %}">
{% endblock %}
