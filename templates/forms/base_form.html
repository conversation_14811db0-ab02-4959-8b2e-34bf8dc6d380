{% extends 'base.html' %}
{% load static %}
{% load vite_tags %}

{% comment %}
Base template for all form-related pages.
Provides consistent structure, styling, and JavaScript loading patterns.
{% endcomment %}

{% block extra_css %}
  <!-- Form-specific CSS -->
  <style>
    /* Form Builder Specific Styles */
    .form-builder-container {
      min-height: calc(100vh - 4rem);
    }

    /* Form Builder App Container */
    .form-builder-app {
      display: flex;
      flex-direction: column;
      min-height: calc(100vh - 8rem);
      position: relative;
    }

    /* Loading States */
    .form-builder-loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.95);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 50;
    }

    .form-builder-error {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.95);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 50;
    }

    .loading-spinner {
      width: 2rem;
      height: 2rem;
      border: 3px solid #e5e7eb;
      border-top: 3px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-progress {
      animation: progress 3s ease-in-out infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes progress {
      0% { width: 0%; }
      50% { width: 70%; }
      100% { width: 100%; }
    }

    /* Static Fallback */
    .static-fallback {
      display: block;
    }

    /* Hide fallback when Vue is mounted */
    .vue-mounted .static-fallback {
      display: none;
    }
    
    /* Smooth transitions for form elements */
    .form-transition {
      transition: all 0.3s ease;
    }
    
    /* Custom scrollbar for form panels */
    .form-panel-scroll {
      scrollbar-width: thin;
      scrollbar-color: #cbd5e0 #f7fafc;
    }
    
    .form-panel-scroll::-webkit-scrollbar {
      width: 6px;
    }
    
    .form-panel-scroll::-webkit-scrollbar-track {
      background: #f7fafc;
    }
    
    .form-panel-scroll::-webkit-scrollbar-thumb {
      background-color: #cbd5e0;
      border-radius: 3px;
    }
    
    .form-panel-scroll::-webkit-scrollbar-thumb:hover {
      background-color: #a0aec0;
    }
    
    /* Animation classes */
    .animate-fade-in {
      animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .animate-slide-in {
      animation: slideIn 0.3s ease-in-out;
    }
    
    @keyframes slideIn {
      from { opacity: 0; transform: translateX(-20px); }
      to { opacity: 1; transform: translateX(0); }
    }
  </style>
  
  {% block form_extra_css %}{% endblock %}
{% endblock %}

{% block content %}
  <div class="form-builder-container">
    {% block form_header %}
      <!-- Default form header - can be overridden -->
      <div class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">
              {% block form_title %}{{ form.name }}{% endblock %}
            </h1>
            <p class="text-sm text-gray-500">
              {% block form_subtitle %}Form Management{% endblock %}
            </p>
          </div>
          <div class="flex space-x-3">
            {% block form_actions %}
              <!-- Default actions - can be overridden -->
              <a href="{% url 'forms:form_detail' form.slug %}" 
                 class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Form
              </a>
            {% endblock %}
          </div>
        </div>
      </div>
    {% endblock %}
    
    {% block form_content %}
      <!-- Main form content area - must be implemented by child templates -->
    {% endblock %}
    
    {% block form_modals %}
      <!-- Modal containers for form-related dialogs -->
    {% endblock %}
  </div>
  
  <!-- Fallback content for non-JS users -->
  <div id="fallback-content" class="hidden min-h-screen bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
      <div class="bg-white p-6 rounded-lg shadow">
        <h1 class="text-2xl font-bold text-gray-900 mb-4">
          {% block fallback_title %}Form Interface{% endblock %}
        </h1>
        <p class="text-gray-600 mb-4">
          {% block fallback_message %}JavaScript is required to use this interface.{% endblock %}
        </p>
        <div class="bg-blue-50 p-4 rounded-lg">
          <p class="text-blue-800 font-medium">Form Information:</p>
          <p class="text-blue-700 mt-1">Name: {{ form.name }}</p>
          <p class="text-blue-700">Status: {{ form.status }}</p>
          <p class="text-blue-700">ID: {{ form.id }}</p>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  {% block form_data_scripts %}
    <!-- Form data initialization scripts -->
  {% endblock %}
  
  {% block form_vue_scripts %}
    <!-- Vue.js and form-specific JavaScript -->
  {% endblock %}
  
  {% block form_extra_js %}
    <!-- Additional form-specific JavaScript -->
  {% endblock %}
{% endblock %}
