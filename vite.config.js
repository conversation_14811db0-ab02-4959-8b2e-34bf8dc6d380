import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  
  // Build configuration
  build: {
    // Output directory relative to project root
    outDir: 'static/dist',
    
    // Generate manifest for Django integration
    manifest: true,
    
    // Multiple entry points
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'static/js/main.js'),
        'form-builder': resolve(__dirname, 'static/js/form-builder/main.js'),
        'form-renderer': resolve(__dirname, 'static/js/form-renderer/main.js'),
        'forms-list': resolve(__dirname, 'static/js/forms-list/main.js'),
        'form-templates': resolve(__dirname, 'static/js/form-templates/main.js'),
      },
      output: {
        // Organize output files
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.')
          const ext = info[info.length - 1]
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `images/[name]-[hash][extname]`
          }
          if (/css/i.test(ext)) {
            return `css/[name]-[hash][extname]`
          }
          return `assets/[name]-[hash][extname]`
        },
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
      }
    },
    
    // Clean output directory before build
    emptyOutDir: true,
  },
  
  // Development server configuration
  server: {
    host: '127.0.0.1',
    port: 3000,
    open: false,
    cors: true,
    
    // Proxy API requests to Django during development
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: false,
      },
      '/media': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: false,
      },
      '/admin': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: false,
      }
    }
  },
  
  // Path resolution
  resolve: {
    alias: {
      '@': resolve(__dirname, 'static/js'),
      '@components': resolve(__dirname, 'static/js/components'),
      '@utils': resolve(__dirname, 'static/js/utils'),
      '@stores': resolve(__dirname, 'static/js/stores'),
      '@assets': resolve(__dirname, 'static/assets'),
      // Use full Vue build with template compiler
      'vue': 'vue/dist/vue.esm-bundler.js'
    }
  },
  
  // CSS configuration
  css: {
    postcss: {
      plugins: [
        require('tailwindcss'),
        require('autoprefixer'),
      ]
    }
  },
  
  // Define global constants
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false,
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false,
  }
})
