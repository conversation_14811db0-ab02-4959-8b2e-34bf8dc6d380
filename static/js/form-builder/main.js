// Form Builder main entry point
import '../main.js' // Import base styles and setup
import { createApp, defineAsyncComponent } from 'vue'
import { createFormBuilderStore } from './stores/formBuilderStore'
import LazyFormBuilder from './components/LazyFormBuilder.vue'

// Global styles
import './styles/main.css'

// Lazy loading manager for form builder sections
class FormBuilderLazyLoader {
  constructor() {
    this.loadedSections = new Set()
    this.loadingPromises = new Map()
    this.errorStates = new Map()
  }

  async loadSection(sectionName) {
    if (this.loadedSections.has(sectionName)) {
      return true
    }

    if (this.loadingPromises.has(sectionName)) {
      return this.loadingPromises.get(sectionName)
    }

    console.log(`Loading form builder section: ${sectionName}`)

    const loadPromise = this._loadSectionComponent(sectionName)
      .then(() => {
        this.loadedSections.add(sectionName)
        this.errorStates.delete(sectionName)
        console.log(`Successfully loaded section: ${sectionName}`)
        return true
      })
      .catch(error => {
        console.error(`Failed to load section ${sectionName}:`, error)
        this.errorStates.set(sectionName, error)
        return false
      })
      .finally(() => {
        this.loadingPromises.delete(sectionName)
      })

    this.loadingPromises.set(sectionName, loadPromise)
    return loadPromise
  }

  async _loadSectionComponent(sectionName) {
    // Simulate network delay for better UX
    await new Promise(resolve => setTimeout(resolve, 100))

    switch (sectionName) {
      case 'canvas':
        return import('./components/FormCanvas.vue')
      case 'palette':
        return import('./components/FieldPalette.vue')
      case 'properties':
        return import('./components/PropertyPanel.vue')
      case 'preview':
        return import('./components/FormPreview.vue')
      case 'customization':
        return import('./components/FormCustomizationPanel.vue')
      case 'settings':
        return import('./components/FormSettingsPanel.vue')
      case 'templates':
        return import('./components/FormLayoutTemplates.vue')
      default:
        throw new Error(`Unknown section: ${sectionName}`)
    }
  }

  getSectionError(sectionName) {
    return this.errorStates.get(sectionName)
  }

  isSectionLoaded(sectionName) {
    return this.loadedSections.has(sectionName)
  }

  isSectionLoading(sectionName) {
    return this.loadingPromises.has(sectionName)
  }
}

// Global lazy loader instance
window.formBuilderLazyLoader = new FormBuilderLazyLoader()

// Create and mount the Vue app
window.initFormBuilder = function(elementId, formData, csrfToken) {
  console.log('initFormBuilder called with:', { elementId, formData, csrfToken })

  try {
    console.log('Creating Vue app...')
    const app = createApp({
      components: {
        LazyFormBuilder
      },
      data() {
        console.log('Vue app data() called with formData:', formData)
        return {
          formData: formData || {},
          csrfToken: csrfToken || ''
        }
      },
      mounted() {
        console.log('Vue app mounted successfully')

        // Emit success event
        document.dispatchEvent(new CustomEvent('vue-form-builder-mounted'))

        // Mark app as Vue-mounted
        const appElement = document.getElementById('form-builder-app')
        if (appElement) {
          appElement.classList.add('vue-mounted')
        }
      },
      errorCaptured(err, instance, info) {
        console.error('Vue error captured:', err, info)
        document.dispatchEvent(new CustomEvent('vue-form-builder-error', {
          detail: err
        }))
        return false
      },
      template: `
        <lazy-form-builder
          :form-data="formData"
          :csrf-token="csrfToken"
        ></lazy-form-builder>
      `
    })

    console.log('Vue app created, setting up providers...')

    // Create and provide the store
    console.log('Creating store...')
    const store = createFormBuilderStore()
    app.provide('formBuilderStore', store)
    app.provide('lazyLoader', window.formBuilderLazyLoader)

    // Global error handler
    app.config.errorHandler = (err, instance, info) => {
      console.error('Form Builder Error:', err, info)

      // Emit error event
      document.dispatchEvent(new CustomEvent('vue-form-builder-error', {
        detail: err
      }))
    }

    console.log('Mounting Vue app to:', elementId)
    // Mount the app
    const mountedApp = app.mount(elementId)
    console.log('Vue app mounted successfully:', mountedApp)

    return app
  } catch (error) {
    console.error('Error in initFormBuilder:', error)
    console.error('Error stack:', error.stack)
    throw error
  }
}

// Initialize the form builder application
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM Content Loaded - Initializing Form Builder')

  const formBuilderElement = document.getElementById('form-builder-app')
  console.log('Form Builder Element:', formBuilderElement)
  console.log('Form Builder Data:', window.formBuilderData)

  if (!formBuilderElement) {
    console.error('Form builder element not found!')
    return
  }

  if (!window.formBuilderData) {
    console.error('Form builder data not found!')
    return
  }

  // Add visible debugging info to the page
  const debugDiv = document.createElement('div')
  debugDiv.style.cssText = 'position: fixed; top: 10px; right: 10px; background: yellow; padding: 10px; z-index: 9999; font-size: 12px; max-width: 300px;'
  debugDiv.innerHTML = `
    <strong>Debug Info:</strong><br>
    Element found: ${!!formBuilderElement}<br>
    Data available: ${!!window.formBuilderData}<br>
    Form data: ${window.formBuilderData ? JSON.stringify(window.formBuilderData.formData).substring(0, 100) + '...' : 'None'}<br>
    Status: Initializing...
  `
  document.body.appendChild(debugDiv)

  try {
    console.log('Initializing Vue app...')
    const app = window.initFormBuilder(
      '#form-builder-app',
      window.formBuilderData.formData,
      window.formBuilderData.csrfToken
    )
    console.log('Vue app initialized successfully:', app)
    debugDiv.innerHTML += '<br>Vue app created successfully!'
  } catch (error) {
    console.error('Error initializing form builder:', error)
    debugDiv.innerHTML += `<br>ERROR: ${error.message}`
    debugDiv.style.background = 'red'
    debugDiv.style.color = 'white'

    // Emit error event
    document.dispatchEvent(new CustomEvent('vue-form-builder-error', {
      detail: error
    }))
  }
})
