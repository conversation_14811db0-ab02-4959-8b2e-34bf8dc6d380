/**
 * Test script for lazy loading implementation
 * This script validates that the lazy loading system works correctly
 */

// Test configuration
const TEST_CONFIG = {
  timeout: 5000, // 5 seconds timeout for each test
  sections: ['canvas', 'palette', 'properties', 'preview', 'customization', 'settings', 'templates']
}

class LazyLoadingTester {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      errors: []
    }
    this.lazyLoader = null
  }

  async runTests() {
    console.log('🧪 Starting Lazy Loading Tests...')
    
    try {
      // Initialize the lazy loader
      await this.initializeLazyLoader()
      
      // Run individual tests
      await this.testLazyLoaderInitialization()
      await this.testSectionLoading()
      await this.testErrorHandling()
      await this.testLoadingStates()
      
      // Report results
      this.reportResults()
      
    } catch (error) {
      console.error('❌ Test suite failed:', error)
      this.results.errors.push(`Test suite error: ${error.message}`)
    }
  }

  async initializeLazyLoader() {
    console.log('📦 Initializing lazy loader...')
    
    // Import the lazy loader class
    const { FormBuilderLazyLoader } = await import('./main.js')
    this.lazyLoader = new FormBuilderLazyLoader()
    
    if (!this.lazyLoader) {
      throw new Error('Failed to initialize lazy loader')
    }
    
    console.log('✅ Lazy loader initialized')
  }

  async testLazyLoaderInitialization() {
    console.log('🔍 Testing lazy loader initialization...')
    
    try {
      // Test that lazy loader has required methods
      const requiredMethods = ['loadSection', 'getSectionError', 'isSectionLoaded', 'isSectionLoading']
      
      for (const method of requiredMethods) {
        if (typeof this.lazyLoader[method] !== 'function') {
          throw new Error(`Missing required method: ${method}`)
        }
      }
      
      // Test initial state
      for (const section of TEST_CONFIG.sections) {
        if (this.lazyLoader.isSectionLoaded(section)) {
          throw new Error(`Section ${section} should not be loaded initially`)
        }
        
        if (this.lazyLoader.isSectionLoading(section)) {
          throw new Error(`Section ${section} should not be loading initially`)
        }
      }
      
      this.results.passed++
      console.log('✅ Lazy loader initialization test passed')
      
    } catch (error) {
      this.results.failed++
      this.results.errors.push(`Initialization test failed: ${error.message}`)
      console.error('❌ Lazy loader initialization test failed:', error.message)
    }
  }

  async testSectionLoading() {
    console.log('🔄 Testing section loading...')
    
    for (const section of TEST_CONFIG.sections) {
      try {
        console.log(`  Loading section: ${section}`)
        
        // Test loading state
        const loadPromise = this.lazyLoader.loadSection(section)
        
        if (!this.lazyLoader.isSectionLoading(section)) {
          throw new Error(`Section ${section} should be in loading state`)
        }
        
        // Wait for loading to complete
        const result = await Promise.race([
          loadPromise,
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Timeout')), TEST_CONFIG.timeout)
          )
        ])
        
        if (!result) {
          throw new Error(`Section ${section} failed to load`)
        }
        
        if (!this.lazyLoader.isSectionLoaded(section)) {
          throw new Error(`Section ${section} should be loaded after successful load`)
        }
        
        if (this.lazyLoader.isSectionLoading(section)) {
          throw new Error(`Section ${section} should not be loading after completion`)
        }
        
        console.log(`  ✅ Section ${section} loaded successfully`)
        
      } catch (error) {
        this.results.failed++
        this.results.errors.push(`Section loading test failed for ${section}: ${error.message}`)
        console.error(`  ❌ Section ${section} loading failed:`, error.message)
      }
    }
    
    this.results.passed++
    console.log('✅ Section loading tests completed')
  }

  async testErrorHandling() {
    console.log('⚠️ Testing error handling...')
    
    try {
      // Test loading non-existent section
      const result = await this.lazyLoader.loadSection('non-existent-section')
      
      if (result) {
        throw new Error('Loading non-existent section should fail')
      }
      
      const error = this.lazyLoader.getSectionError('non-existent-section')
      if (!error) {
        throw new Error('Error should be recorded for failed section')
      }
      
      this.results.passed++
      console.log('✅ Error handling test passed')
      
    } catch (error) {
      this.results.failed++
      this.results.errors.push(`Error handling test failed: ${error.message}`)
      console.error('❌ Error handling test failed:', error.message)
    }
  }

  async testLoadingStates() {
    console.log('🔄 Testing loading states...')
    
    try {
      // Test that already loaded sections return immediately
      const startTime = Date.now()
      await this.lazyLoader.loadSection('canvas') // Should already be loaded
      const endTime = Date.now()
      
      if (endTime - startTime > 100) {
        throw new Error('Already loaded section should return immediately')
      }
      
      this.results.passed++
      console.log('✅ Loading states test passed')
      
    } catch (error) {
      this.results.failed++
      this.results.errors.push(`Loading states test failed: ${error.message}`)
      console.error('❌ Loading states test failed:', error.message)
    }
  }

  reportResults() {
    console.log('\n📊 Test Results:')
    console.log(`✅ Passed: ${this.results.passed}`)
    console.log(`❌ Failed: ${this.results.failed}`)
    
    if (this.results.errors.length > 0) {
      console.log('\n🐛 Errors:')
      this.results.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`)
      })
    }
    
    const totalTests = this.results.passed + this.results.failed
    const successRate = totalTests > 0 ? (this.results.passed / totalTests * 100).toFixed(1) : 0
    
    console.log(`\n📈 Success Rate: ${successRate}%`)
    
    if (this.results.failed === 0) {
      console.log('🎉 All tests passed!')
    } else {
      console.log('⚠️ Some tests failed. Please check the errors above.')
    }
  }
}

// Export for use in browser console or other scripts
if (typeof window !== 'undefined') {
  window.LazyLoadingTester = LazyLoadingTester
  
  // Auto-run tests if in development mode
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    document.addEventListener('DOMContentLoaded', () => {
      // Wait a bit for the form builder to initialize
      setTimeout(async () => {
        if (window.formBuilderLazyLoader) {
          console.log('🧪 Auto-running lazy loading tests...')
          const tester = new LazyLoadingTester()
          tester.lazyLoader = window.formBuilderLazyLoader
          await tester.runTests()
        }
      }, 2000)
    })
  }
}

export { LazyLoadingTester }
