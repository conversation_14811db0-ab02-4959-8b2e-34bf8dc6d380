<template>
  <div class="form-canvas">
    <div v-if="fields.length === 0" class="drop-zone empty-canvas" @drop="onDrop" @dragover.prevent @dragenter.prevent>
      <div class="text-center py-12">
        <svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Start Building Your Form</h3>
        <p class="text-gray-500 mb-4">Drag fields from the sidebar or click to add them</p>
        <div class="drop-zone-text">
          Drop a field here to get started
        </div>
      </div>
    </div>

    <div v-else ref="sortableContainer" class="form-fields-container" @drop="onDrop" @dragover.prevent
      @dragenter.prevent>
      <FormField v-for="(field, index) in fields" :key="field.id" :field="field" :index="index"
        :is-selected="selectedField && selectedField.id === field.id" @field-selected="selectField"
        @field-updated="updateField" @field-deleted="deleteField" @field-duplicated="duplicateField" />

      <!-- Drop zone at the end -->
      <div class="drop-zone end-drop-zone" @drop="onDropAtEnd" @dragover.prevent @dragenter.prevent>
        <div class="drop-zone-text">
          Drop field here to add at the end
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import Sortable from 'sortablejs'
import FormField from './FormField.vue'

export default {
  name: 'FormCanvas',
  components: {
    FormField
  },
  props: {
    fields: {
      type: Array,
      required: true
    },
    selectedField: {
      type: Object,
      default: null
    }
  },
  emits: ['field-selected', 'field-updated', 'field-deleted', 'fields-reordered'],
  setup(props, { emit }) {
    const sortableContainer = ref(null)
    let sortableInstance = null

    // Drag and drop state
    const isDragOver = ref(false)
    const dropZoneActive = ref(null)
    const draggingField = ref(null)
    const dragCounter = ref(0)

    const selectField = (field) => {
      emit('field-selected', field)
    }

    const updateField = (fieldData) => {
      emit('field-updated', fieldData)
    }

    const deleteField = (fieldId) => {
      emit('field-deleted', fieldId)
    }

    const duplicateField = (field) => {
      const duplicatedField = {
        ...field,
        id: `field_${Date.now()}`,
        name: `${field.name}_copy`,
        label: `${field.label} (Copy)`
      }

      // Find the index of the original field and insert after it
      const originalIndex = props.fields.findIndex(f => f.id === field.id)
      const newFields = [...props.fields]
      newFields.splice(originalIndex + 1, 0, duplicatedField)

      emit('fields-reordered', newFields)
    }

    const onDrop = (event) => {
      event.preventDefault()
      const fieldType = event.dataTransfer.getData('text/plain')

      if (fieldType) {
        // Create new field
        const newField = createNewField(fieldType)

        // Add to the beginning if canvas is empty, otherwise handle in sortable
        if (props.fields.length === 0) {
          emit('fields-reordered', [newField])
        }
      }
    }

    const onDropAtEnd = (event) => {
      event.preventDefault()
      const fieldType = event.dataTransfer.getData('text/plain')

      if (fieldType) {
        const newField = createNewField(fieldType)
        emit('fields-reordered', [...props.fields, newField])
      }
    }

    const createNewField = (fieldType) => {
      const fieldId = `field_${Date.now()}`
      const fieldCount = props.fields.length + 1

      const baseField = {
        id: fieldId,
        field_type: fieldType,
        name: `field_${fieldCount}`,
        label: getDefaultLabel(fieldType),
        placeholder: '',
        help_text: '',
        required: false,
        order: fieldCount,
        width: 'full',
        is_visible: true,
        properties: getDefaultProperties(fieldType),
        conditional_logic: {}
      }

      return baseField
    }

    const getDefaultLabel = (fieldType) => {
      const labels = {
        text: 'Text Input',
        textarea: 'Text Area',
        email: 'Email Address',
        url: 'Website URL',
        number: 'Number',
        date: 'Date',
        time: 'Time',
        datetime: 'Date & Time',
        select: 'Dropdown',
        radio: 'Radio Buttons',
        checkbox: 'Checkboxes',
        checkbox_single: 'Checkbox',
        file: 'File Upload',
        image: 'Image Upload',
        rating: 'Rating',
        slider: 'Slider',
        signature: 'Signature',
        section: 'Section Header',
        html: 'HTML Content',
        hidden: 'Hidden Field'
      }

      return labels[fieldType] || 'Form Field'
    }

    const getDefaultProperties = (fieldType) => {
      const defaults = {
        text: { max_length: 255 },
        textarea: { rows: 4, max_length: 2000 },
        email: { validate_email: true },
        url: { validate_url: true },
        number: { min_value: null, max_value: null, step: 1 },
        date: { date_format: 'YYYY-MM-DD' },
        time: { time_format: 'HH:MM' },
        select: { options: [], multiple: false },
        radio: { options: [], layout: 'vertical' },
        checkbox: { options: [], min_selections: null, max_selections: null },
        file: { max_file_size: 5242880, allowed_extensions: ['pdf', 'doc', 'docx'], multiple: false },
        image: { max_file_size: 2097152, allowed_extensions: ['jpg', 'jpeg', 'png'], multiple: false },
        rating: { min_rating: 1, max_rating: 5, step: 1 },
        slider: { min_value: 0, max_value: 100, step: 1, show_value: true },
        section: { level: 2 },
        html: { content: '<p>Enter your HTML content here</p>' }
      }

      return defaults[fieldType] || {}
    }

    const initSortable = () => {
      if (sortableContainer.value) {
        sortableInstance = Sortable.create(sortableContainer.value, {
          animation: 150,
          ghostClass: 'sortable-ghost',
          chosenClass: 'sortable-chosen',
          dragClass: 'sortable-drag',
          handle: '.drag-handle',
          onEnd: (evt) => {
            const newFields = [...props.fields]
            const movedField = newFields.splice(evt.oldIndex, 1)[0]
            newFields.splice(evt.newIndex, 0, movedField)

            // Update order property
            newFields.forEach((field, index) => {
              field.order = index + 1
            })

            emit('fields-reordered', newFields)
          }
        })
      }
    }

    const destroySortable = () => {
      if (sortableInstance) {
        sortableInstance.destroy()
        sortableInstance = null
      }
    }

    onMounted(() => {
      nextTick(() => {
        if (props.fields.length > 0) {
          initSortable()
        }
      })
    })

    onUnmounted(() => {
      destroySortable()
    })

    // Reinitialize sortable when fields change
    const reinitializeSortable = () => {
      destroySortable()
      nextTick(() => {
        if (props.fields.length > 0) {
          initSortable()
        }
      })
    }

    // Watch for fields changes
    watch(() => props.fields.length, (newLength, oldLength) => {
      if ((newLength > 0 && oldLength === 0) || (newLength === 0 && oldLength > 0)) {
        reinitializeSortable()
      }
    })

    return {
      sortableContainer,
      selectField,
      updateField,
      deleteField,
      duplicateField,
      onDrop,
      onDropAtEnd
    }
  }
}
</script>

<style scoped>
.form-canvas {
  @apply min-h-full;
}

.empty-canvas {
  @apply border-2 border-dashed border-gray-300 rounded-lg;
}

.form-fields-container {
  @apply space-y-4;
}

.drop-zone {
  @apply border-2 border-dashed border-transparent rounded-lg p-4 transition-colors;
}

.drop-zone.active {
  @apply border-primary-500 bg-primary-50;
}

.end-drop-zone {
  @apply border-gray-300 text-center py-8;
}

.drop-zone-text {
  @apply text-sm text-gray-500;
}

.sortable-ghost {
  @apply opacity-50;
}

.sortable-chosen {
  @apply ring-2 ring-primary-500;
}

.sortable-drag {
  @apply transform rotate-2 shadow-lg;
}
</style>
