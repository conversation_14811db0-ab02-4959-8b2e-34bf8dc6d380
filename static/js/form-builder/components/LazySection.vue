<template>
  <div class="lazy-section">
    <!-- Loading State -->
    <div v-if="isLoading" class="section-loading">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p class="loading-text">{{ loadingText }}</p>
      </div>
    </div>
    
    <!-- Error State -->
    <div v-else-if="hasError" class="section-error">
      <div class="error-content">
        <svg class="error-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
          </path>
        </svg>
        <h3 class="error-title">Failed to Load Section</h3>
        <p class="error-message">{{ errorMessage }}</p>
        <button @click="retryLoad" class="retry-button">
          Try Again
        </button>
      </div>
    </div>
    
    <!-- Loaded Content -->
    <div v-else-if="isLoaded" class="section-content">
      <slot :component="loadedComponent" :loading="isLoading" :error="hasError"></slot>
    </div>
    
    <!-- Placeholder State -->
    <div v-else ref="sectionRef" class="section-placeholder">
      <div class="placeholder-content">
        <div class="placeholder-icon">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10">
            </path>
          </svg>
        </div>
        <p class="placeholder-text">{{ autoLoad ? 'Loading...' : 'Scroll to load' }}</p>
        <button v-if="!autoLoad" @click="loadSection" class="mt-2 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700">
          Load Now
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, inject, watch, defineAsyncComponent } from 'vue'

export default {
  name: 'LazySection',
  props: {
    section: {
      type: String,
      required: true
    },
    loadingText: {
      type: String,
      default: 'Loading...'
    },
    autoLoad: {
      type: Boolean,
      default: false
    },
    loadDelay: {
      type: Number,
      default: 0
    }
  },
  emits: ['loaded', 'error', 'loading-start'],
  setup(props, { emit }) {
    const lazyLoader = inject('lazyLoader')
    const isLoading = ref(false)
    const hasError = ref(false)
    const isLoaded = ref(false)
    const errorMessage = ref('')
    const loadedComponent = ref(null)
    const retryCount = ref(0)
    const maxRetries = 3
    
    // Intersection observer for lazy loading
    const sectionRef = ref(null)
    const intersectionObserver = ref(null)
    
    const loadSection = async () => {
      if (isLoading.value || isLoaded.value) return
      
      isLoading.value = true
      hasError.value = false
      errorMessage.value = ''
      emit('loading-start', props.section)
      
      try {
        // Add artificial delay if specified
        if (props.loadDelay > 0) {
          await new Promise(resolve => setTimeout(resolve, props.loadDelay))
        }
        
        const success = await lazyLoader.loadSection(props.section)
        
        if (success) {
          // Create the async component
          loadedComponent.value = defineAsyncComponent(() => 
            lazyLoader._loadSectionComponent(props.section)
          )
          
          isLoaded.value = true
          emit('loaded', props.section)
        } else {
          throw new Error(lazyLoader.getSectionError(props.section)?.message || 'Failed to load section')
        }
      } catch (error) {
        console.error(`Error loading section ${props.section}:`, error)
        hasError.value = true
        errorMessage.value = error.message || 'An unexpected error occurred'
        emit('error', props.section, error)
      } finally {
        isLoading.value = false
      }
    }
    
    const retryLoad = async () => {
      if (retryCount.value >= maxRetries) {
        errorMessage.value = `Failed to load after ${maxRetries} attempts`
        return
      }
      
      retryCount.value++
      hasError.value = false
      
      // Exponential backoff
      const delay = Math.pow(2, retryCount.value) * 1000
      await new Promise(resolve => setTimeout(resolve, delay))
      
      await loadSection()
    }
    
    // Setup intersection observer for lazy loading
    const setupIntersectionObserver = () => {
      if (!sectionRef.value || props.autoLoad) return
      
      intersectionObserver.value = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting && !isLoaded.value && !isLoading.value) {
              loadSection()
              // Disconnect after loading starts
              intersectionObserver.value?.disconnect()
            }
          })
        },
        {
          rootMargin: '50px', // Start loading 50px before the section comes into view
          threshold: 0.1
        }
      )
      
      intersectionObserver.value.observe(sectionRef.value)
    }
    
    // Watch for section changes
    watch(() => props.section, () => {
      isLoaded.value = false
      hasError.value = false
      loadedComponent.value = null
      retryCount.value = 0
      
      if (props.autoLoad) {
        loadSection()
      }
    })
    
    onMounted(() => {
      if (props.autoLoad) {
        loadSection()
      } else {
        setupIntersectionObserver()
      }
    })
    
    onUnmounted(() => {
      intersectionObserver.value?.disconnect()
    })
    
    return {
      sectionRef,
      isLoading,
      hasError,
      isLoaded,
      errorMessage,
      loadedComponent,
      loadSection,
      retryLoad
    }
  }
}
</script>

<style scoped>
.lazy-section {
  @apply w-full h-full;
}

.section-loading {
  @apply flex items-center justify-center p-8;
}

.loading-content {
  @apply text-center;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4;
}

.loading-text {
  @apply text-gray-600 text-sm;
}

.section-error {
  @apply flex items-center justify-center p-8;
}

.error-content {
  @apply text-center max-w-sm;
}

.error-icon {
  @apply w-12 h-12 text-red-500 mx-auto mb-4;
}

.error-title {
  @apply text-lg font-semibold text-gray-900 mb-2;
}

.error-message {
  @apply text-gray-600 text-sm mb-4;
}

.retry-button {
  @apply px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors;
}

.section-content {
  @apply w-full h-full;
}

.section-placeholder {
  @apply flex items-center justify-center p-8 opacity-50;
}

.placeholder-content {
  @apply text-center;
}

.placeholder-icon {
  @apply w-8 h-8 text-gray-400 mx-auto mb-2;
}

.placeholder-icon svg {
  @apply w-full h-full;
}

.placeholder-text {
  @apply text-gray-500 text-sm;
}
</style>
