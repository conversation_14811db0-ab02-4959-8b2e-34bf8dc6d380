<template>
  <div>Test Render</div>
  <div class="lazy-form-builder">
    <!-- Error Container -->
    <div id="form-builder-error" class="hidden"></div>
    
    <!-- Main Form Builder Layout -->
    <div class="form-builder-main">
      <!-- Header -->
      <div class="builder-header">
        <div class="header-left">
          <h1 class="builder-title">{{ form.name || 'Form Builder' }}</h1>
          <div class="builder-status">
            <span :class="statusClass">{{ form.status || 'draft' }}</span>
            <span class="text-gray-500 mx-2">•</span>
            <span class="text-gray-600">{{ fields.length }} fields</span>
          </div>
        </div>
        
        <div class="header-actions">
          <button 
            @click="togglePreview"
            :class="['btn', showPreview ? 'btn-primary' : 'btn-secondary']"
          >
            {{ showPreview ? 'Edit' : 'Preview' }}
          </button>
          
          <button 
            @click="saveForm"
            :disabled="saving"
            class="btn btn-primary"
          >
            {{ saving ? 'Saving...' : 'Save' }}
          </button>
        </div>
      </div>

      <!-- Builder Content -->
      <div class="builder-content">
        <!-- Sidebar -->
        <div class="builder-sidebar">
          <!-- Sidebar Tabs -->
          <div class="sidebar-tabs">
            <button
              v-for="tab in sidebarTabs"
              :key="tab.id"
              @click="activeSidebarTab = tab.id"
              :class="['sidebar-tab', { active: activeSidebarTab === tab.id }]"
            >
              {{ tab.label }}
            </button>
          </div>
          
          <!-- Sidebar Content -->
          <div class="sidebar-content">
            <!-- Field Palette -->
            <div v-if="activeSidebarTab === 'fields'" class="sidebar-section">
              <LazySection 
                section="palette" 
                :loading-text="'Loading field palette...'"
                @loaded="onSectionLoaded"
                @error="onSectionError"
              >
                <template #default="{ component }">
                  <component 
                    :is="component" 
                    @field-selected="addField"
                  />
                </template>
              </LazySection>
            </div>
            
            <!-- Design Panel -->
            <div v-else-if="activeSidebarTab === 'design'" class="sidebar-section">
              <LazySection 
                section="customization" 
                :loading-text="'Loading design panel...'"
                @loaded="onSectionLoaded"
                @error="onSectionError"
              >
                <template #default="{ component }">
                  <component 
                    :is="component" 
                    :customization="customization"
                    @update-customization="updateCustomization"
                  />
                </template>
              </LazySection>
            </div>
            
            <!-- Templates Panel -->
            <div v-else-if="activeSidebarTab === 'templates'" class="sidebar-section">
              <LazySection 
                section="templates" 
                :loading-text="'Loading templates...'"
                @loaded="onSectionLoaded"
                @error="onSectionError"
              >
                <template #default="{ component }">
                  <component 
                    :is="component" 
                    @apply-template="applyTemplate"
                  />
                </template>
              </LazySection>
            </div>
            
            <!-- Settings Panel -->
            <div v-else-if="activeSidebarTab === 'settings'" class="sidebar-section">
              <LazySection 
                section="settings" 
                :loading-text="'Loading settings...'"
                @loaded="onSectionLoaded"
                @error="onSectionError"
              >
                <template #default="{ component }">
                  <component 
                    :is="component" 
                    :form="form"
                    @update-form-settings="updateFormSettings"
                  />
                </template>
              </LazySection>
            </div>
          </div>
        </div>

        <!-- Main Canvas Area -->
        <div class="canvas-area">
          <div v-if="!showPreview" class="canvas-container" :style="canvasStyles">
            <LazySection 
              section="canvas" 
              :loading-text="'Loading form canvas...'"
              @loaded="onSectionLoaded"
              @error="onSectionError"
            >
              <template #default="{ component }">
                <component 
                  :is="component"
                  :fields="fields"
                  :selected-field="selectedField"
                  @field-selected="selectField"
                  @field-updated="updateField"
                  @field-deleted="deleteField"
                  @field-duplicated="duplicateField"
                  @field-added="addFieldAtPosition"
                  @fields-reordered="reorderFields"
                />
              </template>
            </LazySection>
          </div>
          
          <!-- Preview Mode -->
          <div v-else class="preview-container">
            <LazySection 
              section="preview" 
              :loading-text="'Loading form preview...'"
              @loaded="onSectionLoaded"
              @error="onSectionError"
            >
              <template #default="{ component }">
                <component 
                  :is="component"
                  :form="form"
                  :fields="fields"
                  @submit="handlePreviewSubmit"
                />
              </template>
            </LazySection>
          </div>
        </div>

        <!-- Properties Panel -->
        <div v-if="!showPreview && selectedField" class="properties-panel">
          <LazySection 
            section="properties" 
            :loading-text="'Loading properties panel...'"
            @loaded="onSectionLoaded"
            @error="onSectionError"
          >
            <template #default="{ component }">
              <component 
                :is="component"
                :field="selectedField"
                @field-updated="updateField"
              />
            </template>
          </LazySection>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, inject } from 'vue'
import { useFormBuilder } from '../composables/useFormBuilder'
import { useFormStyling } from '../composables/useFormStyling'
import { useKeyboardShortcuts } from '../composables/useKeyboardShortcuts'
import LazySection from './LazySection.vue'

export default {
  name: 'LazyFormBuilder',
  components: {
    LazySection
  },
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const lazyLoader = inject('lazyLoader')
    const activeSidebarTab = ref('fields')
    const showPreview = ref(false)
    const saving = ref(false)
    const loadedSections = ref(new Set())
    
    // Form builder composable
    const formBuilder = useFormBuilder(props.formData)
    const { 
      form, 
      fields, 
      selectedField, 
      canUndo, 
      canRedo,
      addField,
      updateField,
      deleteField,
      duplicateField,
      selectField,
      undo,
      redo,
      saveForm: saveFormData
    } = formBuilder
    
    // Form styling composable
    const styling = useFormStyling(props.formData.customization)
    const { customization, applyStylesToDOM } = styling
    
    // Keyboard shortcuts
    useKeyboardShortcuts(formBuilder)
    
    const sidebarTabs = ref([
      { id: 'fields', label: 'Fields' },
      { id: 'design', label: 'Design' },
      { id: 'templates', label: 'Templates' },
      { id: 'settings', label: 'Settings' }
    ])

    const statusClass = computed(() => {
      const status = form.status || 'draft'
      return {
        'status-badge': true,
        'status-draft': status === 'draft',
        'status-published': status === 'published',
        'status-archived': status === 'archived'
      }
    })

    const canvasStyles = computed(() => {
      return {
        backgroundColor: customization.value.colors?.background || '#ffffff',
        fontFamily: customization.value.typography?.fontFamily || 'system-ui',
        fontSize: `${customization.value.typography?.fontSize || 16}px`
      }
    })

    // Section loading handlers
    const onSectionLoaded = (sectionName) => {
      loadedSections.value.add(sectionName)
      console.log(`Section loaded: ${sectionName}`)
    }

    const onSectionError = (sectionName, error) => {
      console.error(`Section error: ${sectionName}`, error)
    }

    // Form actions
    const togglePreview = () => {
      showPreview.value = !showPreview.value
      if (showPreview.value) {
        selectedField.value = null
      }
    }

    const saveForm = async () => {
      saving.value = true
      try {
        await saveFormData()
      } catch (error) {
        console.error('Save error:', error)
      } finally {
        saving.value = false
      }
    }

    const addFieldAtPosition = (fieldType, position) => {
      const newField = addField(fieldType)
      if (position !== undefined) {
        // Reorder fields to place new field at position
        const fieldIndex = fields.value.findIndex(f => f.id === newField.id)
        if (fieldIndex !== -1) {
          const field = fields.value.splice(fieldIndex, 1)[0]
          fields.value.splice(position, 0, field)
        }
      }
      selectField(newField)
    }

    const reorderFields = (newOrder) => {
      fields.value = newOrder
    }

    const updateCustomization = (updates) => {
      Object.assign(customization.value, updates)
      applyStylesToDOM()
    }

    const updateFormSettings = (settings) => {
      Object.assign(form.settings, settings)
    }

    const applyTemplate = (template) => {
      // Apply template logic
      console.log('Applying template:', template)
    }

    const handlePreviewSubmit = (formData) => {
      console.log('Preview form submitted:', formData)
    }

    // Initialize canvas section on mount
    onMounted(() => {
      lazyLoader.loadSection('canvas')
    })
    
    return {
      activeSidebarTab,
      showPreview,
      saving,
      loadedSections,
      sidebarTabs,
      form,
      fields,
      selectedField,
      canUndo,
      canRedo,
      customization,
      statusClass,
      canvasStyles,
      onSectionLoaded,
      onSectionError,
      addField,
      addFieldAtPosition,
      updateField,
      deleteField,
      duplicateField,
      selectField,
      reorderFields,
      updateCustomization,
      updateFormSettings,
      applyTemplate,
      undo,
      redo,
      togglePreview,
      handlePreviewSubmit,
      saveForm
    }
  }
}
</script>
