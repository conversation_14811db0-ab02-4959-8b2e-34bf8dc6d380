// Forms List main entry point
import '../main.js' // Import base styles and setup
import { createApp } from 'vue'
import FormsList from './components/FormsList.vue'

// Initialize the forms list application
document.addEventListener('DOMContentLoaded', () => {
  const formsListElement = document.getElementById('forms-list-app')
  
  if (formsListElement) {
    const app = createApp(FormsList)
    
    // Get API base URL from the element's data attributes
    const apiBaseUrl = formsListElement.dataset.apiBaseUrl || '/forms/api'
    
    // Provide configuration
    app.provide('config', {
      apiBaseUrl
    })
    
    app.mount('#forms-list-app')
    
    console.log('Forms List initialized', { apiBaseUrl })
  }
})
