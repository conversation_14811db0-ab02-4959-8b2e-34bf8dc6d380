<template>
  <div>
    <!-- Error Message -->
    <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <i class="fa-solid fa-exclamation-triangle text-red-400"></i>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error loading forms</h3>
          <div class="mt-2 text-sm text-red-700">
            <p>{{ error }}</p>
          </div>
          <div class="mt-4">
            <button @click="loadForms" class="bg-red-100 hover:bg-red-200 text-red-800 px-3 py-2 rounded text-sm">
              Try Again
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="n in 6" :key="n" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="animate-pulse">
          <div class="flex items-center space-x-2 mb-4">
            <div class="w-10 h-10 bg-gray-200 rounded-lg"></div>
            <div>
              <div class="h-4 bg-gray-200 rounded w-24 mb-2"></div>
              <div class="h-3 bg-gray-200 rounded w-20"></div>
            </div>
          </div>
          <div class="h-3 bg-gray-200 rounded w-full mb-4"></div>
          <div class="flex items-center justify-between">
            <div class="flex space-x-4">
              <div class="h-3 bg-gray-200 rounded w-16"></div>
              <div class="h-3 bg-gray-200 rounded w-20"></div>
            </div>
            <div class="h-5 bg-gray-200 rounded w-12"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Forms Grid -->
    <div v-else-if="forms.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div 
        v-for="form in forms" 
        :key="form.id"
        class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition duration-150"
      >
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-2">
              <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <i class="fa-solid fa-file-text text-primary"></i>
              </div>
              <div>
                <h3 class="font-semibold text-text-primary">{{ form.name }}</h3>
                <p class="text-sm text-text-secondary">{{ formatDate(form.created_at) }}</p>
              </div>
            </div>
            <div class="relative">
              <button 
                @click="toggleDropdown(form.id)"
                class="text-text-secondary hover:text-text-primary"
              >
                <i class="fa-solid fa-ellipsis-v"></i>
              </button>
              <div 
                v-if="activeDropdown === form.id"
                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200"
              >
                <div class="py-1">
                  <a :href="`/forms/${form.slug}/`" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <i class="fa-solid fa-eye mr-2"></i>View
                  </a>
                  <a :href="`/forms/${form.slug}/edit/`" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <i class="fa-solid fa-edit mr-2"></i>Edit
                  </a>
                  <a :href="`/forms/${form.slug}/builder/`" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <i class="fa-solid fa-tools mr-2"></i>Builder
                  </a>
                  <button @click="duplicateForm(form)" class="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <i class="fa-solid fa-copy mr-2"></i>Duplicate
                  </button>
                  <hr class="my-1">
                  <button @click="deleteForm(form)" class="w-full text-left block px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                    <i class="fa-solid fa-trash mr-2"></i>Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
          <p class="text-text-secondary text-sm mb-4">{{ form.description || 'No description provided' }}</p>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4 text-sm text-text-secondary">
              <span><i class="fa-solid fa-list mr-1"></i> {{ form.field_count || 0 }} fields</span>
              <span><i class="fa-solid fa-paper-plane mr-1"></i> {{ form.submission_count || 0 }} submissions</span>
            </div>
            <span :class="getStatusClass(form.status)">{{ form.status_display }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-12">
      <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-gray-100 mb-4">
        <i class="fa-solid fa-file-text text-gray-400 text-3xl"></i>
      </div>
      <h3 class="text-lg font-medium text-text-primary mb-2">No forms yet</h3>
      <p class="text-text-secondary mb-6">Get started by creating your first form</p>
      <a href="/forms/create/" class="bg-primary hover:bg-primary-hover text-white px-6 py-3 rounded-lg transition duration-150 inline-flex items-center">
        <i class="fa-solid fa-plus mr-2"></i>
        Create Your First Form
      </a>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, inject } from 'vue'
import { axios } from '../../main.js'

export default {
  name: 'FormsList',
  setup() {
    const config = inject('config')
    
    const forms = ref([])
    const loading = ref(false)
    const error = ref(null)
    const activeDropdown = ref(null)
    
    const loadForms = async () => {
      loading.value = true
      error.value = null
      
      try {
        const response = await axios.get(`${config.apiBaseUrl}/forms/`)
        forms.value = response.data.results || response.data
      } catch (err) {
        error.value = err.response?.data?.detail || 'Failed to load forms'
        console.error('Error loading forms:', err)
      } finally {
        loading.value = false
      }
    }
    
    const formatDate = (dateString) => {
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = Math.abs(now - date)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays === 1) return 'Yesterday'
      if (diffDays < 7) return `${diffDays} days ago`
      if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`
      if (diffDays < 365) return `${Math.ceil(diffDays / 30)} months ago`
      return `${Math.ceil(diffDays / 365)} years ago`
    }
    
    const getStatusClass = (status) => {
      const classes = {
        'published': 'px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full',
        'draft': 'px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full',
        'archived': 'px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full'
      }
      return classes[status] || 'px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full'
    }
    
    const toggleDropdown = (formId) => {
      activeDropdown.value = activeDropdown.value === formId ? null : formId
    }
    
    const duplicateForm = async (form) => {
      try {
        const response = await axios.post(`${config.apiBaseUrl}/forms/${form.slug}/duplicate/`)
        // Reload forms to show the new duplicate
        await loadForms()
        activeDropdown.value = null
      } catch (err) {
        console.error('Error duplicating form:', err)
        alert('Failed to duplicate form')
      }
    }
    
    const deleteForm = async (form) => {
      if (!confirm(`Are you sure you want to delete "${form.name}"? This action cannot be undone.`)) {
        return
      }
      
      try {
        await axios.delete(`${config.apiBaseUrl}/forms/${form.slug}/`)
        // Remove from local list
        forms.value = forms.value.filter(f => f.id !== form.id)
        activeDropdown.value = null
      } catch (err) {
        console.error('Error deleting form:', err)
        alert('Failed to delete form')
      }
    }
    
    // Close dropdown when clicking outside
    const handleClickOutside = (event) => {
      if (!event.target.closest('.relative')) {
        activeDropdown.value = null
      }
    }
    
    onMounted(() => {
      loadForms()
      document.addEventListener('click', handleClickOutside)
    })
    
    return {
      forms,
      loading,
      error,
      activeDropdown,
      loadForms,
      formatDate,
      getStatusClass,
      toggleDropdown,
      duplicateForm,
      deleteForm
    }
  }
}
</script>
