// Main JavaScript entry point for PDFlex
import '../css/input.css'

// Global utilities and helpers
import { createApp } from 'vue'
import axios from 'axios'

// Configure axios defaults
axios.defaults.xsrfCookieName = 'csrftoken'
axios.defaults.xsrfHeaderName = 'X-CSRFToken'
axios.defaults.withCredentials = true

// Set CSRF token from meta tag
const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
if (csrfToken) {
  axios.defaults.headers.common['X-CSRFToken'] = csrfToken
}

// Global error handler
window.addEventListener('unhandledrejection', event => {
  console.error('Unhandled promise rejection:', event.reason)
})

// Initialize any global components or functionality
document.addEventListener('DOMContentLoaded', () => {
  console.log('PDFlex main.js loaded')
  
  // Initialize any global Vue components
  const globalComponents = document.querySelectorAll('[data-vue-component]')
  globalComponents.forEach(element => {
    const componentName = element.dataset.vueComponent
    console.log(`Initializing Vue component: ${componentName}`)
    
    // This will be expanded as we add more components
    switch (componentName) {
      case 'form-list':
        // Initialize form list component
        break
      case 'form-preview':
        // Initialize form preview component
        break
      default:
        console.warn(`Unknown Vue component: ${componentName}`)
    }
  })
})

// Export utilities for use in other modules
export { axios }
